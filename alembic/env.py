import os
import struct
import sys
from logging.config import fileConfig

from alembic import context
from azure.identity import DefaultAzureCredential
from sqlalchemy import create_engine, event

# For migration to work inside docker image
# Somehow in docker in path current dir is not present
# while locally it is added by default
sys.path.append("")

from dataset_manager.connectors.db_engine import Base  # noqa: F
from dataset_manager.repo.capacity import _DBCapacity  # noqa: F
from dataset_manager.repo.permission_set import _DBPermissionSet  # noqa: F
from dataset_manager.repo.profile import _DBProfile  # noqa: F
from dataset_manager.repo.release import _DBRelease  # noqa: F
from dataset_manager.repo.shard import _DBShard  # noqa: F
from dataset_manager.repo.version import _DBVersion  # noqa: F

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.

version_table_schema = "WebApp"


def run_migrations_offline():
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        version_table_schema=version_table_schema,
    )

    with context.begin_transaction():
        context.run_migrations()


def _inject_azure_credential(engine):
    DB_TOKEN_URL = "https://database.windows.net//.default"
    SQL_COPT_SS_ACCESS_TOKEN = 1256

    azure_credentials = DefaultAzureCredential()

    @event.listens_for(engine, "do_connect")
    def provide_token(dialect, conn_rec, cargs, cparams):
        # remove the "Trusted_Connection" parameter that SQLAlchemy adds
        cargs[0] = cargs[0].replace(";Trusted_Connection=Yes", "")

        # create token credential
        raw_token = azure_credentials.get_token(DB_TOKEN_URL).token.encode("utf-16-le")
        token_struct = struct.pack(f"<I{len(raw_token)}s", len(raw_token), raw_token)

        # apply it to keyword arguments
        cparams["attrs_before"] = {SQL_COPT_SS_ACCESS_TOKEN: token_struct}


def run_migrations_online():
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    sql_alchemy_db_url = os.environ.get(
        "SQLALCHEMY_DATABASE_URL",
        "mssql+pyodbc://indiebi:Password1!@localhost/Shards?driver=ODBC+Driver+17+for+SQL+Server",
    )
    connectable = create_engine(
        sql_alchemy_db_url, connect_args={"check_same_thread": False}
    )

    if "odbc_connect" in sql_alchemy_db_url:
        _inject_azure_credential(connectable)

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            version_table_schema=version_table_schema,
            include_schemas=True,
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
