"""add_permission_set_and_release

Revision ID: 92bb85d857ce
Revises: 9ba33a427408
Create Date: 2022-12-07 13:17:03.796558

"""

import enum

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.mssql import UNIQUEIDENTIFIER

# revision identifiers, used by Alembic.
revision = "92bb85d857ce"
down_revision = "9ba33a427408"
branch_labels = None
depends_on = None


class ReleaseStatus(str, enum.Enum):
    REQUESTED = "Requested"
    DONE = "Done"
    OUTDATED = "Outdated"


def upgrade():
    op.create_table(
        "permission_set",
        sa.Column(
            "uuid",
            UNIQUEIDENTIFIER,
            nullable=False,
            unique=True,
            primary_key=True,
            server_default=sa.func.NEWID(),
        ),
        sa.Column("permission_set", sa.JSON, nullable=False),
        sa.Column("permission_set_hash", sa.NVARCHAR(32), nullable=False, unique=True),
        schema="WebApp",
    )
    op.create_table(
        "release",
        sa.Column(
            "uuid",
            UNIQUEIDENTIFIER,
            nullable=False,
            unique=True,
            primary_key=True,
            server_default=sa.func.NEWID(),
        ),
        sa.Column("studio_id", sa.Integer, nullable=False),
        sa.Column("version_id", sa.String, nullable=False),
        sa.Column("permission_set_uuid", UNIQUEIDENTIFIER, nullable=False),
        sa.Column("creation_timestamp", sa.DateTime, nullable=False),
        sa.Column("status", sa.Enum(ReleaseStatus), nullable=False),
        schema="WebApp",
    )
    op.drop_column("version", "release_for", schema="WebApp")
    op.alter_column(
        "shard",
        "profile_id",
        existing_type=sa.NVARCHAR(40),
        nullable=True,
        schema="WebApp",
    )
    op.add_column(
        "shard",
        sa.Column("permission_set_uuid", UNIQUEIDENTIFIER, nullable=True),
        schema="WebApp",
    )
    op.add_column(
        "profile",
        sa.Column("permission_set_uuid", UNIQUEIDENTIFIER, nullable=True),
        schema="WebApp",
    )


def downgrade():
    op.drop_column("profile", "permission_set_uuid", schema="WebApp")
    op.drop_column("shard", "permission_set_uuid", schema="WebApp")
    op.add_column(
        "version",
        sa.Column("release_for", sa.JSON(), nullable=False),
        schema="WebApp",
    )
    op.alter_column(
        "shard",
        "profile_id",
        existing_type=sa.NVARCHAR(40),
        nullable=False,
        schema="WebApp",
    )
    op.drop_table("permission_set", schema="WebApp")
    op.drop_table("release", schema="WebApp")
