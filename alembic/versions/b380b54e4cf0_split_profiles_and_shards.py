"""split profiles and shards

Revision ID: b380b54e4cf0
Revises: e98585d4f21f
Create Date: 2022-08-05 14:50:05.065068

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.mssql import DATETIME2

# revision identifiers, used by Alembic.
revision = "b380b54e4cf0"
down_revision = "e98585d4f21f"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "profile",
        sa.Column("profile_id", sa.NVARCHAR(40), primary_key=True),
        sa.Column("profile_name", sa.String, nullable=False),
        sa.Column("studio_id", sa.Integer, nullable=False),
        sa.Column("creation_timestamp", DATETIME2, nullable=False),
        sa.Column("active_shard_id", sa.NVARCHAR(40), nullable=True),
        schema="WebApp",
    )

    op.drop_table("shard", schema="WebApp")
    op.create_table(
        "shard",
        sa.Column("id", sa.Integer, primary_key=True),
        sa.Column("studio_id", sa.Integer, nullable=False),
        sa.Column("version", sa.String, nullable=False),
        sa.Column("dataset_id", sa.NVARCHAR(40), nullable=False),
        sa.Column("dataset_name", sa.String, nullable=False),
        sa.Column("workspace_id", sa.NVARCHAR(40), nullable=False),
        sa.Column("workspace_name", sa.String, nullable=False),
        sa.Column("capacity_id", sa.NVARCHAR(40), nullable=False),
        sa.Column("capacity_name", sa.String, nullable=False),
        sa.Column("creation_timestamp", DATETIME2, nullable=False),
        schema="WebApp",
    )


def downgrade():
    op.drop_table("profile", schema="WebApp")
    op.drop_table("shard", schema="WebApp")
    op.create_table(
        "shard",
        sa.Column("studio_id", sa.Integer, primary_key=True),
        sa.Column("dataset_id", sa.String, nullable=True),
        sa.Column("workspace_id", sa.String, nullable=True),
        sa.Column("profile_id", sa.String, nullable=True),
        schema="WebApp",
    )
