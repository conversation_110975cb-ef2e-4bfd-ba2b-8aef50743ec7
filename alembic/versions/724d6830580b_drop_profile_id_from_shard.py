"""drop_profile_id_from_shard

Revision ID: 724d6830580b
Revises: cfb86d7a47f4
Create Date: 2023-02-07 10:11:52.209815

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "724d6830580b"
down_revision = "cfb86d7a47f4"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column("shard", "profile_id", schema="WebApp")
    op.alter_column(
        "shard",
        "permission_set_uuid",
        existing_type=sa.NVARCHAR(40),
        nullable=False,
        schema="WebApp",
    )


def downgrade():
    op.add_column(
        "shard",
        sa.Column("profile_id", sa.String, nullable=True),
        schema="WebApp",
    )
    op.alter_column(
        "shard",
        "permission_set_uuid",
        existing_type=sa.NVARCHAR(40),
        nullable=True,
        schema="WebApp",
    )
