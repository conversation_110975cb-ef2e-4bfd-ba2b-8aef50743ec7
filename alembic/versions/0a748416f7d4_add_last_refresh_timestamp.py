"""add-last-refresh-timestamp

Revision ID: 0a748416f7d4
Revises: 47c54b602d55
Create Date: 2023-07-21 11:28:27.422021

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0a748416f7d4"
down_revision = "47c54b602d55"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "shard",
        sa.Column("last_refresh_timestamp", sa.DateTime, nullable=True),
        schema="WebApp",
    )


def downgrade():
    op.drop_column("shard", "last_refresh_timestamp", schema="WebApp")
