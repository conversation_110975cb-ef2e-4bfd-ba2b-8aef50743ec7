"""alter_dataset_name_from_shard_as_nullable

Revision ID: 203d316d8f1c
Revises: 124580fdcb20
Create Date: 2023-02-17 12:09:56.190092

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "203d316d8f1c"
down_revision = "124580fdcb20"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "shard",
        "dataset_name",
        existing_type=sa.String,
        nullable=True,
        schema="WebApp",
    )


def downgrade():
    op.alter_column(
        "shard",
        "dataset_name",
        existing_type=sa.String,
        nullable=False,
        schema="WebApp",
    )
