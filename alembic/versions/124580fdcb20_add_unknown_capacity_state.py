"""add_unknown_capacity_state

Revision ID: 124580fdcb20
Revises: 724d6830580b
Create Date: 2023-02-07 13:08:04.240769

"""

import enum

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "124580fdcb20"
down_revision = "724d6830580b"
branch_labels = None
depends_on = None


class NewCapacityState(enum.Enum):
    ACTIVE = "Active"
    PAUSED = "Paused"
    UNKNOWN = "Unknown"


class OldCapacityState(enum.Enum):
    ACTIVE = "Active"
    PAUSED = "Paused"


def upgrade():
    op.alter_column(
        "capacity",
        "state",
        existing_type=sa.Enum(NewCapacityState),
        nullable=False,
        schema="WebApp",
    )


def downgrade():
    op.alter_column(
        "capacity",
        "state",
        existing_type=sa.Enum(OldCapacityState),
        nullable=False,
        schema="WebApp",
    )
