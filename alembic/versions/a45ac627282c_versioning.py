"""versioning

Revision ID: a45ac627282c
Revises: b380b54e4cf0
Create Date: 2022-09-28 08:31:18.920754

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "a45ac627282c"
down_revision = "b380b54e4cf0"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "profile",
        sa.Column("active_version_id", sa.NVARCHAR(40), nullable=True),
        schema="WebApp",
    )
    op.execute("UPDATE WebApp.profile SET active_version_id = '2.0.4'")
    op.alter_column(
        "profile",
        "active_version_id",
        existing_type=sa.NVARCHAR(40),
        nullable=False,
        schema="WebApp",
    )

    op.create_table(
        "version",
        sa.Column("id", sa.NVARCHAR(40), primary_key=True),
        sa.Column("shard_version", sa.NVARCHAR(40), nullable=False),
        sa.Column("shard_template_workspace_id", sa.NVARCHAR(40), nullable=False),
        sa.Column("visuals_workspace_id", sa.NVARCHAR(40), nullable=False),
        sa.Column("visuals_dataset_id", sa.NVARCHAR(40), nullable=False),
        sa.Column("visuals_reports_ids", sa.String(), nullable=False),
        sa.Column("visuals_desktop_definitions", sa.String(), nullable=False),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("is_default", sa.Boolean(), nullable=False),
        sa.Column("release_for", sa.JSON(), nullable=False),
        schema="WebApp",
    )


def downgrade():
    op.drop_column("profile", "active_version_id", schema="WebApp")
    op.drop_table("version", schema="WebApp")
