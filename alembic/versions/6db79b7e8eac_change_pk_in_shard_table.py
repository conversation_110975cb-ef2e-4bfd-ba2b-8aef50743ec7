"""change_pk_in_shard_table

Revision ID: 6db79b7e8eac
Revises: de549fb4398a
Create Date: 2022-11-04 12:51:43.790924

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "6db79b7e8eac"
down_revision = "de549fb4398a"
branch_labels = None
depends_on = None


def upgrade():
    connection = op.get_bind()
    shard_pk_constraint_name = connection.execute(
        """SELECT CONSTRAINT_NAME
        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
        WHERE TABLE_NAME = 'shard' AND CONSTRAINT_TYPE = 'PRIMARY KEY'"""
    )

    shard_pk_constraint_name = str(shard_pk_constraint_name.one()[0])

    op.drop_constraint(
        constraint_name=shard_pk_constraint_name,
        table_name="shard",
        schema="WebApp",
    )
    op.create_primary_key(
        constraint_name="pk_shard",
        table_name="shard",
        columns=["workspace_id"],
        schema="WebApp",
    )
    op.create_unique_constraint(
        constraint_name="pk_unique",
        table_name="shard",
        columns=["workspace_id"],
        schema="WebApp",
    )
    op.drop_column(table_name="shard", column_name="id", schema="WebApp")


def downgrade():
    op.add_column(
        table_name="shard",
        column=sa.Column("id", sa.Integer, nullable=True),
        schema="WebApp",
    )
    op.alter_column(
        existing_type=sa.Integer,
        table_name="shard",
        column_name="id",
        autoincrement=True,
        existing_autoincrement=True,
        nullable=False,
        schema="WebApp",
    )
    op.drop_constraint(constraint_name="pk_shard", table_name="shard")
    op.create_primary_key(
        constraint_name="pk_shard",
        table_name="shard",
        columns=["id"],
        schema="WebApp",
    )
