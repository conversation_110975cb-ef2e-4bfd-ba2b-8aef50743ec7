"""add_profile_id_to_dbshard

Revision ID: 062326a9412e
Revises: 4957dcc414ec
Create Date: 2022-10-24 16:10:36.453823

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "062326a9412e"
down_revision = "4957dcc414ec"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "shard",
        sa.Column("profile_id", sa.NVARCHAR(40), nullable=True),
        schema="WebApp",
    )


def downgrade():
    op.drop_column("shard", "profile_id", schema="WebApp")
