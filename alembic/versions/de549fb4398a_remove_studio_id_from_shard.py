"""remove_studio_id_from_shard

Revision ID: de549fb4398a
Revises: 062326a9412e
Create Date: 2022-11-04 11:20:36.875714

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "de549fb4398a"
down_revision = "062326a9412e"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column("shard", "studio_id", schema="WebApp")
    op.alter_column(
        "shard",
        "profile_id",
        existing_type=sa.NVARCHAR(40),
        nullable=False,
        schema="WebApp",
    )


def downgrade():
    op.add_column(
        "shard",
        sa.Column("studio_id", sa.Integer, nullable=True),
        schema="WebApp",
    )
    op.alter_column(
        "shard",
        "profile_id",
        existing_type=sa.NVARCHAR(40),
        nullable=True,
        schema="WebApp",
    )
