"""indexes

Revision ID: 47c54b602d55
Revises: 99698031169c
Create Date: 2023-07-04 13:46:34.688070

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "47c54b602d55"
down_revision = "99698031169c"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "shard",
        "version",
        existing_type=sa.NVARCHAR(),
        type_=sa.NVARCHAR(50),
        nullable=False,
        schema="WebApp",
    )
    op.create_index(
        op.f("ix_WebApp_profile_studio_id"),
        "profile",
        ["studio_id"],
        unique=True,
        schema="WebApp",
    )
    op.create_index(
        op.f("ix_WebApp_shard_permission_set_uuid"),
        "shard",
        ["permission_set_uuid"],
        unique=False,
        schema="WebApp",
    )
    op.create_index(
        op.f("ix_WebApp_shard_version"),
        "shard",
        ["version"],
        unique=False,
        schema="WebApp",
    )


def downgrade():
    op.drop_index(
        op.f("ix_WebApp_shard_version"),
        table_name="shard",
        schema="WebApp",
    )
    op.drop_index(
        op.f("ix_WebApp_shard_permission_set_uuid"),
        table_name="shard",
        schema="WebApp",
    )
    op.drop_index(
        op.f("ix_WebApp_profile_studio_id"),
        table_name="profile",
        schema="WebApp",
    )
    op.alter_column(
        "shard",
        "version",
        existing_type=sa.NVARCHAR(50),
        type_=sa.NVARCHAR(),
        nullable=False,
        schema="WebApp",
    )
