"""initial migration

Revision ID: e98585d4f21f
Revises:
Create Date: 2022-08-02 18:37:50.552519

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "e98585d4f21f"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "shard",
        sa.Column("studio_id", sa.Integer, primary_key=True),
        sa.Column("dataset_id", sa.String, nullable=True),
        sa.Column("workspace_id", sa.String, nullable=True),
        sa.Column("profile_id", sa.String, nullable=True),
        schema="WebApp",
    )


def downgrade():
    op.drop_table("shard", schema="WebApp")
