"""add_capacities_table

Revision ID: ad0ce9fe4a18
Revises: 6db79b7e8eac
Create Date: 2022-11-10 02:07:19.684024

"""

import enum

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "ad0ce9fe4a18"
down_revision = "6db79b7e8eac"
branch_labels = None
depends_on = None


class CapacityState(enum.Enum):
    ACTIVE = "Active"
    PAUSED = "Paused"


def upgrade():
    op.create_table(
        "capacity",
        sa.Column("id", sa.NVARCHAR(40), nullable=False, primary_key=True),
        sa.Column("name", sa.NVARCHAR(40), nullable=False),
        sa.Column("tier", sa.NVARCHAR(40), nullable=False),
        sa.Column("state", sa.Enum(CapacityState), nullable=False),
        sa.Column("is_default", sa.<PERSON>(), nullable=False),
        schema="WebApp",
    )


def downgrade():
    op.drop_table("capacity", schema="WebApp")
