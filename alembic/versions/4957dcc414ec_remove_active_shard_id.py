"""remove_active_shard_id

Revision ID: 4957dcc414ec
Revises: a45ac627282c
Create Date: 2022-10-10 11:36:15.290079

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "4957dcc414ec"
down_revision = "a45ac627282c"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column("profile", "active_shard_id", schema="WebApp")


def downgrade():
    op.add_column(
        "profile",
        sa.Column("active_shard_id", sa.NVARCHAR(40), nullable=True),
        schema="WebApp",
    )
