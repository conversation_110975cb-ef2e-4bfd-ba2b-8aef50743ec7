"""drop_column_capacity_name_from_shard

Revision ID: cfb86d7a47f4
Revises: 92bb85d857ce
Create Date: 2023-01-25 13:56:08.707609

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "cfb86d7a47f4"
down_revision = "92bb85d857ce"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column("shard", "capacity_name", schema="WebApp")


def downgrade():
    op.add_column(
        "shard",
        sa.Column("capacity_name", sa.String, nullable=True),
        schema="WebApp",
    )
