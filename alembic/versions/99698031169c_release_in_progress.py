"""release-in-progress

Revision ID: 99698031169c
Revises: 203d316d8f1c
Create Date: 2023-02-22 15:50:38.123278

"""

import enum

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "99698031169c"
down_revision = "203d316d8f1c"
branch_labels = None
depends_on = None


class NewReleaseStatus(str, enum.Enum):
    REQUESTED = "Requested"
    REQUESTED_SHARD = "RequestedShard"
    REQUESTED_ASSIGN = "RequestedAssign"
    DONE = "Done"
    OUTDATED = "Outdated"
    FAILED = "Failed"
    CANCELED = "Canceled"


class OldReleaseStatus(str, enum.Enum):
    REQUESTED = "Requested"
    DONE = "Done"
    OUTDATED = "Outdated"


def upgrade():
    op.alter_column(
        "release",
        "status",
        existing_type=sa.Enum(NewReleaseStatus),
        nullable=False,
        schema="WebApp",
    )
    op.add_column(
        "release",
        sa.Column(
            "is_full_recreate",
            sa.<PERSON>an(),
            nullable=False,
            server_default=sa.sql.false(),
        ),
        schema="WebApp",
    )
    op.add_column(
        "release",
        sa.Column(
            "try_count",
            sa.Integer,
            nullable=False,
            server_default="0",
        ),
        schema="WebApp",
    )


def downgrade():
    op.alter_column(
        "release",
        "status",
        existing_type=sa.Enum(OldReleaseStatus),
        nullable=False,
        schema="WebApp",
    )
    op.drop_column("release", "is_full_recreate", schema="WebApp")
    op.drop_column("release", "try_count", schema="WebApp")
