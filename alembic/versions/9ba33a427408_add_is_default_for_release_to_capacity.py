"""add_is_default_for_release_to_capacity

Revision ID: 9ba33a427408
Revises: ad0ce9fe4a18
Create Date: 2022-11-21 15:37:04.218396

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "9ba33a427408"
down_revision = "ad0ce9fe4a18"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "capacity",
        sa.Column("is_default_for_releases", sa.<PERSON>(), nullable=False),
        schema="WebApp",
    )


def downgrade():
    op.drop_column("capacity", "is_default_for_releases", schema="WebApp")
