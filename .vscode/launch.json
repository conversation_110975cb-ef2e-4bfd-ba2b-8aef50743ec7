{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Dataset Manager: API",
            "type": "debugpy",
            "request": "launch",
            "module": "uvicorn",
            "args": ["dataset_manager.main:app", "--reload"],
            "jinja": true,
            "justMyCode": false
        },
        {
            "name": "Update demo mode cfg",
            "type": "debugpy",
            "request": "launch",
            "program": "./scripts/update_demo_mode_cfg.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "args": ["--workspace_id", "${input:workspace_id}"]
        },
        {
            "name": "Python: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": true
        },
        {
            "name": "Python: Debug Tests",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "purpose": ["debug-test"],
            "console": "integratedTerminal",
            "justMyCode": false
        }
    ],
    "inputs": [
        {
            "id": "workspace_id",
            "type": "promptString",
            "default": "9c0778ee-6f7c-4e4f-b1ed-294f90bc7887",
            "description": "PowerBI workspace ID with demo model and visuals"
        }
    ]
}
