#!/bin/bash

set -e # exit immediately on failures

# colors
Black='\033[0;30m'        # Black
Red='\033[0;31m'          # Red
Green='\033[0;32m'        # Green
Yellow='\033[0;33m'       # Yellow
Blue='\033[0;34m'         # Blue
Purple='\033[0;35m'       # Purple
Cyan='\033[0;36m'         # <PERSON>an
White='\033[0;37m'        # White

if [ -z "$DEV_USERNAME" ] ; then
    echo "DEV_USERNAME environment variable is not set! Tests will fail."
    echo "Did you run the dev setup script?"
    echo "Did you forgot to run 'source ~/.bashrc'?"
    exit 1
fi

cd "$(dirname "$0")"/..

DATA_REPOS=(
    dataviz-team,dataset-manager
    dataviz-team,dataviz-dashboards
    dataviz-team,dataviz-model
    data-pipeline,report-processor
    data-pipeline,data-jobs
    data-pipeline,synapse
)

for group_and_repo in "${DATA_REPOS[@]}"; do
    IFS=',' read group repo <<< "${group_and_repo}"
    if [ ! -d "$repo" ] ; then
        echo -e "$Green $repo: Cloning repo... $White"
        git clone "**************:bluebrick/indiebi/$group/$repo.git"
    else 
        echo -e "$Yellow $repo: Repo already cloned SKIP$White"
    fi
done

for group_and_repo in "${DATA_REPOS[@]}"; do
    IFS=',' read group repo <<< "${group_and_repo}"
    cd $repo

    # For now we can't use setup.sh because for report-processor we don't want to run terraform
    if [ -f "./pyproject.toml" ]; then
        echo -e "$Green $repo: Installing dependencies... $White"
        poetry env use 3.10
        poetry install
    else 
        echo -e "$Yellow $repo: Repo doesn't use poetry... SKIP$White"
    fi

    # if [ -f "./scripts/setup.sh" ]; then
    #     echo -e "$Green $repo: Setting up repo... $White"
    #     source ./scripts/setup.sh
    # else 
    #     echo -e "$Yellow $repo: ./scripts/setup.sh does not exist. SKIP$White"
    # fi
    cd ..
done
