


poetry run save_version --version_path=version_gk.json --visuals_dir_path="PBIX Visuals" --dataset_path="PBIX Model/dev.pbix" --url="https://dm.indiebi.dev/" --auth_key="pikachu"


SELECT
  STRING_AGG(studio_id, ', ') WITHIN GROUP (ORDER BY studio_id) AS studio_ids
FROM [WebApp].[profile]
WHERE active_version_id = '2.6.3'



let config = {
    type: 'report',
    tokenType: models.TokenType.Embed,
    accessToken: accessToken, // The access token should have permissions for all artifacts.
    embedUrl: embedUrl,
    id: "reportId", // The id of the report that you want to embed
    permissions: permissions,
    datasetBinding: {
        datasetId: "2640a3e2-2707-43b6-a3b0-8fb83c69b39f", // The dataset id that you want the report to use 
    },
};

token dzierżawy: bb7887c7-c8cd-4f3d-b602-2e270710fdb9



poetry run save_version --version_path=version.json --visuals_dir_path="pbi_visuals" --dataset_path="pbi_model/prod.pbix" --url="https://dm.indiebi.com" --auth_key="pikachu"



func init LocalFunctionProj --python

az config param-persist on
az group create --name rg-gkocjan-azure-madness --location westeurope
az storage account create --name gkocjanazuremadness --sku Standard_LRS
az functionapp create --consumption-plan-location westeurope --runtime python --runtime-version 3.10 --functions-version 4 --name func-gkocjan-azure-madness --os-type linux --storage-account gkocjanazuremadness
func azure functionapp publish func-gkocjan-azure-madness





find . -type f -name "*" -print0 | xargs -0 sed -i '' -e 's/dlsaggregatedprod/dlsaggregateddev/g'

find . -type f -name "*" -exec sed -i'' -e 's/dlsaggregatedprod/dlsaggregateddev/g' {} +





old_query = """RVZBTFVBVEUKCVZBUiBUaHJlc2hvbGRzVmFsdWVzID0gQUREQ09MVU1OUygKICAgIEdFTkVSQVRFU0VSSUVTKDEsIDI1MCwgMSksCiAgICAidGhyZXNob2xkVmFsdWUiLCBbVmFsdWVdKjI1MDAwMAoJKQoJVkFSIE1heEN1bXVsYXRpdmVTYWxlcyA9IE1BWFgoCgkJR1JPVVBCWSgKCQkJJ2ZhY3Rfc2FsZXNfY3VtdWxhdGl2ZScsCgkJCSdmYWN0X3NhbGVzX2N1bXVsYXRpdmUnW2RhdGVdLAoJCQkiVG90YWxHcm9zc1NhbGVzIiwKCQkJU1VNWCgKCQkJCUNVUlJFTlRHUk9VUCgpLAoJCQkJJ2ZhY3Rfc2FsZXNfY3VtdWxhdGl2ZSdbZ3Jvc3Nfc2FsZXNfY3VtdWxhdGl2ZV0KCQkJKQoJCSksCgkJW1RvdGFsR3Jvc3NTYWxlc10KCSkKCVZBUiBGYWN0U2FsZXNXaXRoRGVsdGEgPSBBRERDT0xVTU5TKAoJCUdST1VQQlkoCgkJCSdmYWN0X3NhbGVzX2N1bXVsYXRpdmUnLAoJCQknZmFjdF9zYWxlc19jdW11bGF0aXZlJ1tkYXRlXSwKCQkJImdyb3NzX3NhbGVzX2N1bXVsYXRpdmUiLAoJCQlTVU1YKAoJCQkJQ1VSUkVOVEdST1VQKCksCgkJCQknZmFjdF9zYWxlc19jdW11bGF0aXZlJ1tncm9zc19zYWxlc19jdW11bGF0aXZlXQoJCQkpCgkJKSwKCQkiU2FsZXNEZWx0YSIsIE1heEN1bXVsYXRpdmVTYWxlcyAtIFtncm9zc19zYWxlc19jdW11bGF0aXZlXQoJKQoJVkFSIExhdGVzdERhdGVXaXRoTWF4U2FsZXMgPSBDQUxDVUxBVEUoCgkJTUFYKCdmYWN0X3NhbGVzX2N1bXVsYXRpdmUnW2RhdGVdKSwKCQlGSUxURVIoCgkJCUZhY3RTYWxlc1dpdGhEZWx0YSwKCQkJW2dyb3NzX3NhbGVzX2N1bXVsYXRpdmVdID0gTWF4Q3VtdWxhdGl2ZVNhbGVzCgkJKQoJKQoJVkFSIEZhY3RTYWxlc1dpdGhEZWx0YUNsZWFuZWQgPSBGSUxURVIoCgkJRmFjdFNhbGVzV2l0aERlbHRhLAoJCSdmYWN0X3NhbGVzX2N1bXVsYXRpdmUnW2RhdGVdIDw9IExhdGVzdERhdGVXaXRoTWF4U2FsZXMKCSkKCVZBUiBDcm9zc0pvaW5lZCA9IENST1NTSk9JTigKCQlUaHJlc2hvbGRzVmFsdWVzLAoJCUZhY3RTYWxlc1dpdGhEZWx0YUNsZWFuZWQKCSkKCVZBUiBGaWx0ZXJlZEZvclRocmVzaG9sZHMgPSBGSUxURVIoCgkJQ3Jvc3NKb2luZWQsCgkJW1NhbGVzRGVsdGFdID4gW3RocmVzaG9sZFZhbHVlXQoJKQoJVkFSIFRocmVzaG9sZHNXaXRoRGF0ZXMgPSBHUk9VUEJZKAoJCUZpbHRlcmVkRm9yVGhyZXNob2xkcywKCQlbdGhyZXNob2xkVmFsdWVdLAoJCSJsYXRlc3REYXRlIiwKCQlNQVhYKAoJCQlDVVJSRU5UR1JPVVAoKSwKCQkJJ2ZhY3Rfc2FsZXNfY3VtdWxhdGl2ZSdbZGF0ZV0KCQkpCgkpCglSRVRVUk4KCQlUaHJlc2hvbGRzV2l0aERhdGVz"""
new_query = """RVZBTFVBVEUKVkFSIFRocmVzaG9sZHNWYWx1ZXMgPSBBRERDT0xVTU5TKAogICAgR0VORVJBVEVTRVJJRVMoMSwgMjUwLCAxKSwgIAogICAgInRocmVzaG9sZFZhbHVlIiwgW1ZhbHVlXSoyNTAwMDAKCSkKVkFSIE1heERhdGUgPSAKICAgIE1BWChmYWN0X3NhbGVzW2RhdGVdKQpWQVIgRmFjdFNhbGVzV2l0aEN1bXVsYXRpdmUgPSAKICAgIEFERENPTFVNTlMoCiAgICAgICAgU1VNTUFSSVpFKAogICAgICAgICAgICBmYWN0X3NhbGVzLAogICAgICAgICAgICBmYWN0X3NhbGVzW2RhdGVdCiAgICAgICAgKSwKICAgICAgICAiUmV2ZXJzZUN1bXVsYXRpdmUiLCAKICAgICAgICBWQVIgQ3VycmVudERhdGUgPSBmYWN0X3NhbGVzW2RhdGVdCiAgICAgICAgUkVUVVJOCiAgICAgICAgQ0FMQ1VMQVRFKAogICAgICAgICAgICBTVU0oZmFjdF9zYWxlc1tncm9zc19zYWxlc10pLAogICAgICAgICAgICBGSUxURVIoCiAgICAgICAgICAgICAgICBBTExTRUxFQ1RFRChmYWN0X3NhbGVzKSwKICAgICAgICAgICAgICAgIGZhY3Rfc2FsZXNbZGF0ZV0gPj0gQ3VycmVudERhdGUgJiYgZmFjdF9zYWxlc1tkYXRlXSA8PSBNYXhEYXRlCiAgICAgICAgICAgICkKICAgICAgICApCiAgICApClZBUiBDcm9zc0pvaW5lZCA9IAogICAgQ1JPU1NKT0lOKAogICAgICAgIFRocmVzaG9sZHNWYWx1ZXMsCiAgICAgICAgRmFjdFNhbGVzV2l0aEN1bXVsYXRpdmUKICAgICkKVkFSIEZpbHRlcmVkRm9yVGhyZXNob2xkcyA9IAogICAgRklMVEVSKAogICAgICAgIENyb3NzSm9pbmVkLAogICAgICAgIFtSZXZlcnNlQ3VtdWxhdGl2ZV0gPj0gW1RocmVzaG9sZFZhbHVlXQogICAgKQpWQVIgVGhyZXNob2xkc1dpdGhEYXRlcyA9IAogICAgR1JPVVBCWSgKICAgICAgICBGaWx0ZXJlZEZvclRocmVzaG9sZHMsCiAgICAgICAgW1RocmVzaG9sZFZhbHVlXSwKICAgICAgICAiQm9yZGVyRGF0ZSIsIE1BWFgoQ1VSUkVOVEdST1VQKCksIFtkYXRlXSAtIDEpCiAgICApClJFVFVSTgogICAgRElTVElOQ1QoCgkJU0VMRUNUQ09MVU1OUygKCQkJVGhyZXNob2xkc1dpdGhEYXRlcywKCQkJInRocmVzaG9sZFZhbHVlIiwgW1RocmVzaG9sZFZhbHVlXSwKCQkJImxhdGVzdERhdGUiLCBbQm9yZGVyRGF0ZV0KCQkpCgkp"""
studio_ids = [
    209,
    403,
    790,
    446,
    796,
    705,
    516,
    183,
    722,
    386,
    492,
    351,
    621,
    809,
    846,
    14,
    467,
    787,
    922,
    971,
    982,
    1054,
    1131,
    1139,
    1261,
    1627,
    1652,
    1654,
    1714,
    1274,
    1286,
    1334,
    1346,
    1355,
    1387,
    1388,
    1401,
    1437,
    1455,
    1508,
    1575,
    1593,
    1706,
    1709,
    1715,
    1713,
    10020,
    10034,
    10053,
    10050,
    10087,
    10057,
    10064,
    10105,
    10068,
    10112,
    10094,
    297,
    703,
    22,
    417,
    409,
    751,
    499,
    240,
    253,
    344,
    483,
    223,
    330,
    657,
    810,
    589,
    671,
    860,
    873,
    999,
    1032,
    1034,
    1095,
    1097,
    1156,
    1187,
    1216,
    1552,
    1574,
    1628,
    1649,
    1727,
    1353,
    1374,
    1447,
    1586,
    1604,
    1588,
    1690,
    10014,
    10083,
    10084,
    10090,
    10060,
    10109,
    10069,
    8,
    2,
    468,
    245,
    282,
    707,
    9,
    519,
    75,
    561,
    895,
    757,
    29,
    898,
    938,
    1006,
    1018,
    1049,
    1062,
    1085,
    1115,
    1203,
    1554,
    1585,
    1626,
    1642,
    1651,
    1679,
    1695,
    1712,
    1303,
    1419,
    1434,
    1478,
    1498,
    10013,
    10024,
    10025,
    10028,
    10035,
    10039,
    10042,
    10044,
    10051,
    10074,
    10076,
    10078,
    10089,
    10070,
    214,
    611,
    149,
    799,
    311,
    313,
    488,
    136,
    764,
    332,
    355,
    786,
    750,
    822,
    872,
    984,
    1061,
    1144,
    763,
    339,
    1504,
    1579,
    1717,
    222,
    1432,
    1454,
    448,
    1531,
    1683,
    1697,
    10015,
    10030,
    10031,
    10046,
    289,
    10086,
    10104,
    10071,
    10095,
]
# studio_ids = [209]


@pytest.mark.parametrize(
    "studio_id",
    studio_ids,
    ids=lambda x: x,
)
def test_run(studio_id):
    import requests

    headers = {
        "Content-Type": "application/json",
        "x-api-key": "ibit-4mbqQTqcSpb0mWTc5fajF1teh2JxCndEVidVMgN5pwbhZl3oZz1cntDRmVpU0SmVScd6W1uYjmR0RWL4t36jLwVempKCOSvN9RnSFe8iS070kXDDN6BuvqvNen2",
    }

    old_response = requests.post(
        f"https://dataset-manager.indiebi.com/studio/{studio_id}/query",
        json={"base64_query": old_query},
        headers=headers,
    )
    assert old_response.status_code == 200

    new_response = requests.post(
        f"https://dataset-manager.indiebi.com/studio/{studio_id}/query",
        json={"base64_query": new_query},
        headers=headers,
    )
    assert new_response.status_code == 200

    assert old_response.json() == new_response.json()
