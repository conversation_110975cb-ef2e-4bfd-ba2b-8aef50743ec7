from fastapi import APIRouter, Depends

from dataset_manager.api.api_tags import ApiTag
from dataset_manager.api.dependencies import (
    get_capacieties_api_dependency,
    get_dataset_api_dependency,
    get_repo,
)
from dataset_manager.azure.management.capacities import CapacietiesAPI
from dataset_manager.azure.powerbi.datasets import DatasetAPI
from dataset_manager.entities import ShardWorkspaceId, StudioId
from dataset_manager.repo import Repo

router = APIRouter(prefix="/debug")


@router.get("/studio_id/{studio_id}", tags=[ApiTag.PRIVATE])
def studio(
    studio_id: StudioId,
    repo: Repo = Depends(get_repo),
    dataset_api: DatasetAPI = Depends(get_dataset_api_dependency),
) -> dict:
    shard = repo.shard.get_for_studio_id_and_version(studio_id=studio_id)
    refresh_history = dataset_api.all_refreshes(
        workspace_id=shard.workspace_id, dataset_id=shard.dataset_id
    )
    return {
        "shard": shard,
        "refresh_history": refresh_history,
    }


@router.get("/shard/{shard_id}", tags=[ApiTag.PRIVATE])
def workspace(
    shard_id: ShardWorkspaceId,
    repo: Repo = Depends(get_repo),
    dataset_api: DatasetAPI = Depends(get_dataset_api_dependency),
) -> dict:
    shard = repo.shard.get_by_workspace_id(workspace_id=shard_id)
    refresh_history = dataset_api.all_refreshes(
        workspace_id=shard.workspace_id, dataset_id=shard.dataset_id
    )
    return {
        "shard": shard,
        "refresh_history": refresh_history,
    }


@router.get("/capacities", tags=[ApiTag.PRIVATE])
def capacities(
    repo: Repo = Depends(get_repo),
    capacieties_api: CapacietiesAPI = Depends(get_capacieties_api_dependency),
) -> dict:
    all_capacities = repo.capacity.all()
    return {
        capacity.name: {
            "db_state": capacity,
            "actual_state": capacieties_api.details(capacity.name),
        }
        for capacity in all_capacities
    }
