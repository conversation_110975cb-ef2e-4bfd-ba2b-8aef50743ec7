import logging
from enum import Enum
from pathlib import Path

from fastapi import APIRouter, Depends, File, Form, UploadFile
from sqlalchemy.orm import Session

from dataset_manager.api.api_tags import ApiTag
from dataset_manager.api.dependencies import (
    Repo,
    WorkspaceAPI,
    get_model_container,
    get_repo,
    get_workspace_api_dependency,
)
from dataset_manager.azure.blob import Model<PERSON>ontainer
from dataset_manager.config import get_config
from dataset_manager.connectors.db_engine import get_db
from dataset_manager.logic.version import (
    NewVersionRequest,
    NewVersionUseCase,
    PBIXFile,
    set_default_version,
)
from dataset_manager.repo.profile import ProfileRepo
from dataset_manager.repo.release import CreateRelease, ReleaseRepo
from dataset_manager.repo.version import ShardVersion, Version, VersionId, VersionRepo

log = logging.getLogger(__name__)
router = APIRouter(prefix="/version")


@router.get("/default", response_model=Version, tags=[ApiTag.PRIVATE])
def get_version(
    session: Session = Depends(get_db),
):
    return VersionRepo(session=session).default()


@router.get("/all", response_model=list[Version], tags=[ApiTag.PRIVATE])
def get_all_versions(
    session: Session = Depends(get_db),
):
    return VersionRepo(session=session).get_all()


@router.get("/{version_id}", response_model=Version, tags=[ApiTag.PRIVATE])
def get_specific_version(
    version_id: VersionId,
    session: Session = Depends(get_db),
):
    return VersionRepo(session=session).get(version_id=version_id)


class ReeleaseMode(str, Enum):
    dev_recreate = "dev_recreate"
    force_recreate = "force_recreate"


@router.put(
    "/{version_id}/release_for/all", response_model=Version, tags=[ApiTag.PRIVATE]
)
@router.put(
    "/{version_id}/release_for/all/{release_mode}",
    response_model=Version,
    tags=[ApiTag.PRIVATE],
)
def version_release_for_all_studios(
    version_id: VersionId,
    release_mode: ReeleaseMode = ReeleaseMode.dev_recreate,
    session: Session = Depends(get_db),
):
    version = VersionRepo(session=session).get(version_id=version_id)

    release_repo = ReleaseRepo(session=session)

    if release_mode == ReeleaseMode.force_recreate:
        is_full_recreate = True
    else:
        is_full_recreate = version.full_recreate_on_release

    new_releases = []
    for profile in ProfileRepo(session=session).get_all():
        assert profile.permission_set_uuid is not None
        new_release = CreateRelease(
            studio_id=profile.studio_id,
            version_id=version.id,
            permission_set_uuid=profile.permission_set_uuid,
            is_full_recreate=is_full_recreate,
        )
        new_releases.append(new_release)
    release_repo.create_bulk(new_releases=new_releases)

    return version


@router.put("/{version_id}/release_for", response_model=Version, tags=[ApiTag.PRIVATE])
@router.put(
    "/{version_id}/release_for/{release_mode}",
    response_model=Version,
    tags=[ApiTag.PRIVATE],
)
def version_release_for(
    version_id: VersionId,
    studio_ids: list[int],
    release_mode: ReeleaseMode = ReeleaseMode.dev_recreate,
    session: Session = Depends(get_db),
):
    version = VersionRepo(session=session).get(version_id=version_id)

    profile_repo = ProfileRepo(session=session)
    release_repo = ReleaseRepo(session=session)

    if release_mode == ReeleaseMode.force_recreate:
        is_full_recreate = True
    else:
        is_full_recreate = version.full_recreate_on_release

    new_releases = []
    for studio_id in studio_ids:
        profile = profile_repo.get_for_studio(studio_id=studio_id)
        assert profile.permission_set_uuid is not None
        new_release = CreateRelease(
            studio_id=profile.studio_id,
            version_id=version.id,
            permission_set_uuid=profile.permission_set_uuid,
            is_full_recreate=is_full_recreate,
        )
        new_releases.append(new_release)
    release_repo.create_bulk(new_releases=new_releases)

    return VersionRepo(session=session).save(version)


@router.put("/{version_id}/set_default", response_model=Version, tags=[ApiTag.PRIVATE])
def set_default(version_id: VersionId, session: Session = Depends(get_db)):
    return set_default_version(session, version_id)


@router.post("/{version_id}", response_model=Version, tags=[ApiTag.PRIVATE])
def post_new_version(
    version_id: VersionId,
    shard_version: ShardVersion | None = Form(None),
    dataset: UploadFile | None = File(None),
    visuals: list[UploadFile] = File(description="Multiple files as UploadFile"),
    repo: Repo = Depends(get_repo),
    workspace_api: WorkspaceAPI = Depends(get_workspace_api_dependency),
    model_container: ModelContainer = Depends(get_model_container),
    config=Depends(get_config),
):
    pbix_dataset = (
        PBIXFile(name=dataset.filename, content=dataset.file.read())
        if dataset and dataset.filename is not None
        else None
    )

    assert all(visual.filename is not None for visual in visuals)

    pbix_visuals = []  # Can't use list comprehension because mypy complains about type
    for visual in visuals:
        assert visual.filename is not None
        pbix_visuals.append(PBIXFile(name=visual.filename, content=visual.file.read()))

    request = NewVersionRequest(
        version_id=version_id,
        shard_version=ShardVersion(shard_version or version_id),
        dataset=pbix_dataset,
        visuals=pbix_visuals,
        workspaces_prefix=config.ENV_PREFIX + config.ENV,
        desktop_definitions_template_path=Path(
            "static/desktop-definitions-template.json"
        ),
    )

    response = NewVersionUseCase(
        repo=repo,
        workspace_api=workspace_api,
        model_container=model_container,
        request=request,
    ).process()
    return response.version
