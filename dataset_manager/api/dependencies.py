from functools import partial

from azure.storage.blob import BlobServiceClient
from fastapi import Depends
from sqlalchemy.orm import Session

from dataset_manager.azure.blob import ModelContainer
from dataset_manager.azure.client import aio_auth_token_provider, auth_token_provider
from dataset_manager.azure.identity import credential
from dataset_manager.azure.management.capacities import get_capacieties_api
from dataset_manager.azure.powerbi.datasets import (
    AioDatasetAPI,
    DatasetAPI,
    aio_get_dataset_api,
    get_dataset_api,
)
from dataset_manager.azure.powerbi.workspaces import WorkspaceAPI, get_workspace_api
from dataset_manager.config import get_config
from dataset_manager.connectors.db_engine import get_db
from dataset_manager.repo import Repo
from dataset_manager.repo import get_repo as _get_repo


def get_repo(session: Session = Depends(get_db)) -> Repo:
    return _get_repo(session=session)


def get_management_auth_token_provider(config=Depends(get_config)):
    return partial(auth_token_provider, config.AZURE_MNG_TOKEN_URL)


def get_capacieties_api_dependency(
    config=Depends(get_config),
    management_auth_token_provider=Depends(get_management_auth_token_provider),
):
    return get_capacieties_api(
        config=config, auth_token_provider=management_auth_token_provider
    )


def get_powerbi_auth_token_provider(config=Depends(get_config)):
    return partial(auth_token_provider, config.PBI_TOKEN_URL)


def get_powerbi_aio_auth_token_provider(config=Depends(get_config)):
    return partial(aio_auth_token_provider, config.PBI_TOKEN_URL)


def get_workspace_api_dependency(
    powerbi_auth_token_provider=Depends(get_powerbi_auth_token_provider),
) -> WorkspaceAPI:
    return get_workspace_api(auth_token_provider=powerbi_auth_token_provider)


def get_dataset_api_dependency(
    powerbi_auth_token_provider=Depends(get_powerbi_auth_token_provider),
) -> DatasetAPI:
    return get_dataset_api(auth_token_provider=powerbi_auth_token_provider)


def get_aio_dataset_api_dependency(
    powerbi_auth_token_provider=Depends(get_powerbi_aio_auth_token_provider),
) -> AioDatasetAPI:
    return aio_get_dataset_api(auth_token_provider=powerbi_auth_token_provider)


def get_model_container(config=Depends(get_config)):
    return ModelContainer(
        client=BlobServiceClient(
            account_url=config.STORAGE_ACCOUNT_URL, credential=credential
        )
    )
