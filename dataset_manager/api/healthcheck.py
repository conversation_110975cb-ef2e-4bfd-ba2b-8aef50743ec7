import logging
import os
import time
from datetime import datetime
from functools import partial
from typing import Any, Dict

import psutil
from azure.core.exceptions import AzureError
from azure.storage.blob import ContainerClient
from dateutil.relativedelta import relativedelta
from fastapi import APIRouter, Depends
from sqlalchemy import exc, func, select, table
from sqlalchemy.orm import Session
from starlette.requests import Request

from dataset_manager.api.api_tags import ApiTag
from dataset_manager.azure.blob import MODELS_CONTAINER_NAME
from dataset_manager.azure.client import AzureAPIClient, auth_token_provider
from dataset_manager.azure.exceptions import PBIError
from dataset_manager.azure.identity import credential
from dataset_manager.azure.powerbi.service import request
from dataset_manager.config import (
    APP_VERSION,
    AZURE_MNG_TOKEN_URL,
    AZURE_SUBSCRIPTION_ID,
    DOCKER_BUILD_TIMESTAMP,
    DOCKER_TAG,
    HEALTHCHECK_URL,
    SERVICE_NAME,
    STORAGE_ACCOUNT_URL,
)
from dataset_manager.connectors.db_engine import get_db

router = APIRouter(prefix=HEALTHCHECK_URL)


# filter healthcheck access logs
class EndpointLogFilter(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        return record.getMessage().find(HEALTHCHECK_URL) == -1


logging.getLogger("uvicorn.access").addFilter(EndpointLogFilter())


@router.get("", tags=[ApiTag.PRIVATE])
async def health(request: Request):
    p = psutil.Process(os.getpid()).create_time()
    uptime = relativedelta(datetime.now(), datetime.fromtimestamp(p))
    response: Dict[str, Any] = {}
    response["serviceName"] = SERVICE_NAME
    response["instance"] = os.uname()
    response["version"] = APP_VERSION
    response["docker"] = {}
    response["docker"]["tag"] = DOCKER_TAG
    response["docker"]["buildTimestamp"] = DOCKER_BUILD_TIMESTAMP
    response["uptime"] = {}
    response["uptime"]["seconds"] = uptime.seconds
    response["uptime"]["readable"] = (
        f"{uptime.years} years, {uptime.months} months, {uptime.days} days, {uptime.hours} hours, {uptime.minutes} minutes, {uptime.seconds} seconds"
    )

    return response


def get_container_client():
    with ContainerClient(
        STORAGE_ACCOUNT_URL,
        MODELS_CONTAINER_NAME,
        credential,
    ) as container_client:
        yield container_client


def get_azure_api_client():
    management_auth_token_provider = partial(auth_token_provider, AZURE_MNG_TOKEN_URL)
    return AzureAPIClient(management_auth_token_provider)


@router.get("/synthetic", tags=[ApiTag.PRIVATE])
def synthetic_health(
    session: Session = Depends(get_db),
    container_client: ContainerClient = Depends(get_container_client),
    azure_api_client: AzureAPIClient = Depends(get_azure_api_client),
):
    response: dict[str, bool | float | None] = {
        "database_connected": False,
        "database_duration": None,
        "storage_connected": False,
        "storage_duration": None,
        "pbi_api_available": False,
        "pbi_api_duration": None,
        "azure_api_available": False,
        "azure_api_duration": None,
    }
    try:
        start = time.perf_counter()
        tables_count = session.scalar(
            select(func.count()).select_from(  # pylint: disable=not-callable
                table("tables", schema="sys")
            )
        )
        response["database_duration"] = time.perf_counter() - start
        response["database_connected"] = bool(tables_count)
    except exc.SQLAlchemyError:
        pass

    try:
        start = time.perf_counter()
        response["storage_connected"] = container_client.exists()
        response["storage_duration"] = time.perf_counter() - start
    except AzureError:
        pass

    try:
        start = time.perf_counter()
        request("get", "https://api.powerbi.com/v1.0/myorg/capacities")
        response["pbi_api_duration"] = time.perf_counter() - start
        response["pbi_api_available"] = True
    except Exception:
        pass

    try:
        start = time.perf_counter()
        azure_api_client.get(
            f"https://management.azure.com/subscriptions/{AZURE_SUBSCRIPTION_ID}/providers/Microsoft.PowerBIDedicated/capacities?api-version=2021-01-01"
        )
        response["azure_api_duration"] = time.perf_counter() - start
        response["azure_api_available"] = True
    except PBIError:
        pass

    return response
