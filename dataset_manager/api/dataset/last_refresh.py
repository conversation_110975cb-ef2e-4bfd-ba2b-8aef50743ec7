from fastapi import Depends

from dataset_manager.api.api_tags import Api<PERSON>ag
from dataset_manager.api.dataset.dataset import ShardDependency, router
from dataset_manager.api.dependencies import get_dataset_api_dependency
from dataset_manager.azure.powerbi.datasets import DatasetAPI
from dataset_manager.connectors.db_engine import get_session_context
from dataset_manager.logic.shard import fill_last_refresh_timestamp


@router.get(
    "/last-refresh",
    tags=[ApiTag.PUBLIC],
)
def get_last_refresh_date(
    shard: ShardDependency,
    dataset_api: DatasetAPI = Depends(get_dataset_api_dependency),
    db_context=Depends(get_session_context),
):
    if shard.last_refresh_timestamp is None:
        with db_context() as session:
            shard = fill_last_refresh_timestamp(
                session=session, dataset_api=dataset_api, shard=shard
            )
    return shard.last_refresh_timestamp
