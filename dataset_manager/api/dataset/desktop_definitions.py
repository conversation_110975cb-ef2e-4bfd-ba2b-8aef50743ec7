from fastapi import Depends
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from dataset_manager.api.api_tags import ApiTag
from dataset_manager.api.dataset.dataset import StudioIdDependency, router
from dataset_manager.connectors.db_engine import get_db
from dataset_manager.repo.profile import ProfileRepo
from dataset_manager.repo.version import VersionId, VersionRepo


@router.get("/desktop-definitions", tags=[ApiTag.PUBLIC])
@router.get("/desktop-definitions/version/{requested_version}", tags=[ApiTag.PUBLIC])
def get_desktop_definitions(
    studio_id: StudioIdDependency,
    requested_version: VersionId | None = None,
    session: Session = Depends(get_db),
):
    profile = ProfileRepo(session=session).get_for_studio(studio_id=studio_id)
    version = VersionRepo(session=session).get_extended(
        requested_version or profile.active_version_id
    )

    return JSONResponse(content=jsonable_encoder(version.visuals_desktop_definitions))
