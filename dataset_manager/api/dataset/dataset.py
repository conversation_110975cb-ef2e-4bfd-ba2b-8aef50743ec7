from typing import Annotated, Callable, ContextManager

from fastapi import APIRouter, Depends, <PERSON>, Request
from sqlalchemy.orm import Session

from dataset_manager.connectors.db_engine import get_session_context
from dataset_manager.connectors.user_service import get_legacy_studio_id_from_user_id
from dataset_manager.repo.shard import Shard, ShardRepo

router = APIRouter()


def store_user_id(request: Request, user_id: Annotated[str, Path(...)]):
    request.scope["user_id"] = user_id


def store_studio_id(request: Request, studio_id: Annotated[int, Path(...)]):
    request.scope["studio_id"] = studio_id


DBContextDependency = Annotated[
    Callable[[], ContextManager[Session]], Depends(get_session_context)
]


async def get_studio_id(request: Request) -> int:
    assert "user_id" in request.scope or "studio_id" in request.scope

    if "user_id" in request.scope:
        return await get_legacy_studio_id_from_user_id(request.scope["user_id"])
    else:
        return request.scope["studio_id"]


StudioIdDependency = Annotated[int, Depends(get_studio_id)]


def get_shard(
    request: Request, db_context: DBContextDependency, studio_id: StudioIdDependency
) -> Shard:
    if "shard" in request.scope:
        return request.scope["shard"]

    with db_context() as session:
        return ShardRepo(session=session).get_for_studio_id_and_version(
            studio_id=studio_id
        )


ShardDependency = Annotated[Shard, Depends(get_shard)]
