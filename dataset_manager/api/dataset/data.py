from fastapi import APIRouter, Depends

from dataset_manager.api.api_tags import ApiTag
from dataset_manager.api.dataset.dataset import ShardDependency
from dataset_manager.api.dependencies import (
    get_aio_dataset_api_dependency,
)
from dataset_manager.azure.powerbi.datasets import AioDatasetAP<PERSON>
from dataset_manager.entities import SKU, DaxQuery, Portal, Region, Slicer, Studio

router = APIRouter()


def _convert_sku_from_dax(table_row: dict) -> SKU:
    return SKU(
        unique_sku_id=table_row["[unique_sku_id]"],
        base_sku_id=table_row["[base_sku_id]"],
        portal_platform_region=table_row["[portal_platform_region]"],
        gso=table_row["[gso]"],
        name=table_row["[human_name]"],
        product_name=table_row["[product_name]"],
        sku_type=table_row["[sku_type]"],
        studio_id=table_row["[studio_id]"],
        product_id=table_row["[product_id]"],
        release_date=table_row["[release_date]"],
    )


def _convert_portal_from_dax(table_row: dict) -> Portal:
    return Portal(
        portal_platform_region=table_row["[portal_platform_region]"],
        portal=table_row["[portal]"],
        platform=table_row["[platform]"],
        region=table_row["[region]"],
        store=table_row["[store]"],
        abbreviated_name=table_row["[abbreviated_name]"],
    )


def _convert_studio_from_dax(table_row: dict) -> Studio:
    return Studio(
        company_name=table_row["dim_studio[company_name]"],
        studio_id=table_row["dim_studio[studio_id]"],
    )


def _convert_region_from_dax(table_row: dict) -> Region:
    return Region(
        region=table_row["dim_country[region]"],
        country_name=table_row["dim_country[country_name]"],
    )


@router.get("/sku", tags=[ApiTag.PUBLIC])
async def get_skus(
    shard: ShardDependency,
    dataset_api: AioDatasetAPI = Depends(get_aio_dataset_api_dependency),
) -> list[SKU]:
    query = """
            EVALUATE SELECTCOLUMNS(dim_sku,
            "unique_sku_id", [unique_sku_id],
            "base_sku_id", [base_sku_id],
            "portal_platform_region", [portal_platform_region],
            "gso", [gso],
            "human_name", [human_name],
            "product_name", RELATED(dim_products[product_name]),
            "sku_type", [sku_type],
            "studio_id", RELATED(dim_products[studio_id]),
            "product_id", [product_id],
            "release_date", [release_date])
            """
    raw_result = await dataset_api.execute_dax(
        str(shard.workspace_id),
        str(shard.dataset_id),
        query,
    )
    return list(map(_convert_sku_from_dax, raw_result))


@router.get("/portal", tags=[ApiTag.PUBLIC])
async def get_portals(
    shard: ShardDependency,
    dataset_api: AioDatasetAPI = Depends(get_aio_dataset_api_dependency),
) -> list[Portal]:
    query = """
            EVALUATE SELECTCOLUMNS(dim_portals,
            "portal_platform_region", [portal_platform_region],
            "portal", [portal],
            "platform", [platform],
            "region", [region],
            "store", [store],
            "abbreviated_name", [abbreviated_name]
            )"""
    raw_response = await dataset_api.execute_dax(
        str(shard.workspace_id),
        str(shard.dataset_id),
        query,
    )
    return list(map(_convert_portal_from_dax, raw_response))


revenue_measures = [
    {
        "label": "Gross Revenue",
        "value": "'Core Measures'[Gross Sales Total]",
        "type": "revenue",
    },
    {
        "label": "Refunded revenue",
        "value": "'Core Measures'[Gross Returned]",
        "type": "revenue",
    },
    {
        "label": "Revenue (excl. refunds)",
        "value": "'Core Measures'[Gross Sales]",
        "type": "revenue",
    },
    {
        "label": "Approx. net revenue",
        "value": "'Core Measures'[Approx Net Revenue]",
        "type": "revenue",
    },
]

units_measures = [
    {
        "label": "All units",
        "value": "'Core Measures'[Units Total]",
        "type": "units",
    },
    {
        "label": "Units (excl. returns)",
        "value": "'Core Measures'[Units]",
        "type": "units",
    },
    {
        "label": "Refunded units",
        "value": "'Core Measures'[Units Returned]",
        "type": "units",
    },
    {
        "label": "Free units",
        "value": "'Core Measures'[Units Freely Distributed]",
        "type": "units",
    },
    {
        "label": "Activated retail units",
        "value": "'Core Measures'[Units Sold in Retail]",
        "type": "units",
    },
]

units_measures_excl_returns = [
    {
        "label": "Units (excl. returns)",
        "value": "'Core Measures'[Units]",
        "type": "units",
    },
]

wishlist_measures = [
    {
        "label": "Wishlist adds",
        "value": "'Core Measures'[Wishlist Additions]",
        "type": "wishlist",
    },
    {
        "label": "Wishlist activations",
        "value": "'Core Measures'[Wishlist Purchases, Activations & Gifts]",
        "type": "wishlist",
    },
    {
        "label": "Wishlist deletes",
        "value": "'Core Measures'[Wishlist Deletes]",
        "type": "wishlist",
    },
    {
        "label": "Non-wishlists Purchases",
        "value": "'Core Measures'[Non-Wishlist Purchases]",
        "type": "wishlist",
    },
]

visibility_measures = [
    {
        "label": "Non-owner impressions",
        "value": "'Core Measures'[Non-owner Impressions]",
        "type": "visibility",
    },
    {
        "label": "Non-owner visits",
        "value": "'Core Measures'[Non-owner Visits]",
        "type": "visibility",
    },
]

all_measures = (
    revenue_measures + units_measures + wishlist_measures + visibility_measures
)


@router.get("/slicer/{slicer_name}", tags=[ApiTag.PUBLIC])
async def get_slicer_data(
    slicer_name: Slicer,
    shard: ShardDependency,
    dataset_api: AioDatasetAPI = Depends(get_aio_dataset_api_dependency),
):
    if slicer_name == Slicer.SELECTED_MEASURE_ALL:
        return all_measures
    elif slicer_name == Slicer.SELECTED_MEASURE_REVENUE:
        return revenue_measures
    elif slicer_name == Slicer.SELECTED_MEASURE_UNITS:
        return units_measures
    elif slicer_name == Slicer.SELECTED_MEASURE_UNITS_EXCL_RETURNS:
        return units_measures_excl_returns
    elif slicer_name == Slicer.SELECTED_MEASURE_WISHLIST:
        return wishlist_measures
    elif slicer_name == Slicer.SELECTED_MEASURE_VISIBILITY:
        return visibility_measures
    elif slicer_name == Slicer.SALE_DAY_TYPE:
        return ["promo", "regular"]

    if slicer_name == Slicer.STUDIO:
        query = "EVALUATE dim_studio"
        return list(
            map(
                _convert_studio_from_dax,
                await dataset_api.execute_dax(
                    str(shard.workspace_id),
                    str(shard.dataset_id),
                    query,
                ),
            )
        )
    elif slicer_name == Slicer.REGION:
        query = """
        EVALUATE
        SELECTCOLUMNS( dim_country, dim_country[region], dim_country[country_name] )
        ORDER BY
            dim_country[region],
            dim_country[country_name]
        """
        return list(
            map(
                _convert_region_from_dax,
                await dataset_api.execute_dax(
                    str(shard.workspace_id),
                    str(shard.dataset_id),
                    query,
                ),
            )
        )
    else:
        raise Exception("Unsuported slicer")


@router.post("/query", tags=[ApiTag.PUBLIC])
async def get_query_result(
    body: DaxQuery,
    shard: ShardDependency,
    dataset_api: AioDatasetAPI = Depends(get_aio_dataset_api_dependency),
):
    return await dataset_api.execute_dax(
        str(shard.workspace_id),
        str(shard.dataset_id),
        body.query,
    )
