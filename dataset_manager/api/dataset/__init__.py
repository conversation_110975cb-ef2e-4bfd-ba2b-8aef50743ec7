from fastapi import APIRouter, Depends

from dataset_manager.api.dataset import (
    dashboards_auth_token,
    data,
    dataset,
    desktop_definitions,
    last_refresh,
    shard,
)

__all__ = ["dataset"]

outer_router = APIRouter()

outer_router.include_router(
    dataset.router,
    prefix="/studio/{studio_id}",
    dependencies=[Depends(dataset.store_studio_id)],
    deprecated=True,
)
outer_router.include_router(
    data.router,
    prefix="/studio/{studio_id}",
    dependencies=[Depends(dataset.store_studio_id)],
    deprecated=True,
)
outer_router.include_router(
    dataset.router,
    prefix="/dataset/by-user/{user_id}",
    dependencies=[Depends(dataset.store_user_id)],
)
outer_router.include_router(
    data.router,
    prefix="/dataset/by-user/{user_id}",
    dependencies=[Depends(dataset.store_user_id)],
)
