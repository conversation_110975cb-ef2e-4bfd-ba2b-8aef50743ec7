import logging
from enum import Enum

from fastapi import API<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Query
from fastapi.concurrency import run_in_threadpool
from pydantic import BaseModel
from sqlalchemy.orm import Session

from dataset_manager.api.api_tags import ApiTag
from dataset_manager.api.dataset.data import DaxQuery
from dataset_manager.api.dependencies import (
    get_aio_dataset_api_dependency,
    get_dataset_api_dependency,
    get_repo,
    get_workspace_api_dependency,
)
from dataset_manager.azure.cache import cache
from dataset_manager.azure.powerbi.datasets import AioDatasetAPI, DatasetAPI, PBIRefresh
from dataset_manager.azure.powerbi.workspaces import WorkspaceAPI
from dataset_manager.config import get_config
from dataset_manager.connectors.db_engine import get_db, get_session_context
from dataset_manager.entities import ShardWorkspaceId
from dataset_manager.logic.shard import assign_to_capacity
from dataset_manager.repo import Repo
from dataset_manager.repo.capacity import CapacityName
from dataset_manager.repo.permission_set import Permission
from dataset_manager.repo.profile import Profile
from dataset_manager.repo.shard import Shard, ShardRepo

log = logging.getLogger(__name__)
router = APIRouter(prefix="/shard")


class AssignCapacity(BaseModel):
    capacity_name: CapacityName


class RefreshType(Enum):
    FULL = "full"
    INCREMENTAL = "incremental"


@router.get("/active", response_model=list[Shard], tags=[ApiTag.PRIVATE])
def get_shard_list_by_given_studios(
    studio_ids: list[int] | None = Query(default=None),
    repo: Repo = Depends(get_repo),
):
    result = repo.shard.search(studio_ids=studio_ids)
    return sorted(result, key=lambda shard: shard.workspace_id)


@router.get("/{shard_id}", response_model=Shard, tags=[ApiTag.PRIVATE])
def get_one_shard(
    shard_id: ShardWorkspaceId,
    session: Session = Depends(get_db),
):
    return ShardRepo(session=session).get_by_workspace_id(workspace_id=shard_id)


@router.get("/{shard_id}/profiles", response_model=list[Profile], tags=[ApiTag.PRIVATE])
def get_shard_profiles(shard_id: ShardWorkspaceId, repo: Repo = Depends(get_repo)):
    shard = repo.shard.get_by_workspace_id(workspace_id=shard_id)
    return repo.profile.get_all_for_permission_set_uuid(
        permission_set_uuid=shard.permission_set_uuid
    )


@router.post("/{shard_id}/query", tags=[ApiTag.PRIVATE])
async def post_shard_dax_query(
    shard_id: ShardWorkspaceId,
    body: DaxQuery,
    session_contex=Depends(get_session_context),
    dataset_api: AioDatasetAPI = Depends(get_aio_dataset_api_dependency),
):
    def _get_shard():
        with session_contex() as session:
            return ShardRepo(session=session).get_by_workspace_id(workspace_id=shard_id)

    shard = await run_in_threadpool(_get_shard)

    return await dataset_api.execute_dax(
        str(shard.workspace_id),
        str(shard.dataset_id),
        body.query,
    )


@router.put(
    "/{workspace_id}/assign-to-capacity", response_model=Shard, tags=[ApiTag.PRIVATE]
)
def assign_shard_to_capacity(
    workspace_id: ShardWorkspaceId,
    capacity_to_assign: AssignCapacity,
    repo: Repo = Depends(get_repo),
    workspace_api: WorkspaceAPI = Depends(get_workspace_api_dependency),
):
    shard = repo.shard.get_by_workspace_id(workspace_id=workspace_id)
    capacity = repo.capacity.get_by_name(name=capacity_to_assign.capacity_name)
    return assign_to_capacity(
        repo=repo, workspace_api=workspace_api, shard=shard, capacity=capacity
    )


@router.put("/{workspace_id}/add-debug-admins", tags=[ApiTag.PRIVATE])
def add_debug_admins(
    workspace_id: ShardWorkspaceId,
    config=Depends(get_config),
    workspace_api: WorkspaceAPI = Depends(get_workspace_api_dependency),
):
    workspace_api.assing_admins(workspace_id=workspace_id, admins=config.DEBUG_ADMINS)


@router.get(
    "/{workspace_id}/refresh-status",
    response_model=PBIRefresh,
    tags=[ApiTag.PRIVATE],
)
def refresh_shard_status(
    workspace_id: ShardWorkspaceId,
    repo: Repo = Depends(get_repo),
    dataset_api: DatasetAPI = Depends(get_dataset_api_dependency),
):
    shard = repo.shard.get_by_workspace_id(workspace_id=workspace_id)
    last_refresh = dataset_api.last_refresh(
        workspace_id=workspace_id, dataset_id=shard.dataset_id
    )
    if last_refresh.status.is_completed():
        repo.shard.save(
            shard.model_copy(update={"last_refresh_timestamp": last_refresh.end_time})
        )
        cache.reset(key=shard.workspace_id)
    return last_refresh


@router.post("/{workspace_id}/refresh/{refresh_type}", tags=[ApiTag.PRIVATE])
def refresh_shard(
    workspace_id: ShardWorkspaceId,
    refresh_type: RefreshType,
    ignore: str | None = Header(default=None),
    repo: Repo = Depends(get_repo),
    dataset_api: DatasetAPI = Depends(get_dataset_api_dependency),
):
    if ignore is not None:  # Ignore refresh from NDP2 until full migration
        return

    shard = repo.shard.get_by_workspace_id(workspace_id=workspace_id)
    allow_incremental = refresh_type == RefreshType.INCREMENTAL
    dataset_api.start_refresh(
        workspace_id=workspace_id,
        dataset_id=shard.dataset_id,
        allow_incremental=allow_incremental,
    )


@router.get(
    "/{shard_id}/permission-set", response_model=list[Permission], tags=[ApiTag.PRIVATE]
)
def get_shard_permission_set(
    shard_id: ShardWorkspaceId, repo: Repo = Depends(get_repo)
):
    shard = repo.shard.get_by_workspace_id(workspace_id=shard_id)
    return repo.permission_set.get(uuid=shard.permission_set_uuid).permission_set
