import secrets

from fastapi import HTT<PERSON><PERSON>x<PERSON>
from fastapi.param_functions import Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from starlette.requests import Request
from starlette.status import HTTP_403_FORBIDDEN

from dataset_manager.config import API_KEY, HEALTHCHECK_URL

api_key_header_check = APIKeyHeader(name="x-api-key", auto_error=False)


def require_key(
    api_key_header: str = Security(api_key_header_check),
    request: Request = None,  # type: ignore
):
    if request is not None and request.url.path == HEALTHCHECK_URL:  # type: ignore
        return
    if not secrets.compare_digest(api_key_header or "", API_KEY):
        raise HTTPException(status_code=HTTP_403_FORBIDDEN, detail="Not authenticated")
