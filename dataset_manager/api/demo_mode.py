import json

from fastapi import API<PERSON>outer, Depends, Request
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from dataset_manager.api.api_tags import ApiTag
from dataset_manager.api.dataset import data
from dataset_manager.azure.powerbi.embed import generate_simple_embed_token
from dataset_manager.config import SettingsDependency
from dataset_manager.entities import (
    DatasetId,
    ShardWorkspaceId,
    VersionId,
)

router = APIRouter(prefix="/demo-mode")


class SimplifiedShard(BaseModel):
    dataset_id: DatasetId
    workspace_id: ShardWorkspaceId


def store_shard(request: Request, settings: SettingsDependency):
    request.scope["studio_id"] = (
        None  # Workaround for get_studio_id to work with static demo mode studio
    )
    request.scope["shard"] = SimplifiedShard(
        dataset_id=settings.demo_mode.dateset_id,
        workspace_id=settings.demo_mode.workspace_id,
    )


router.include_router(
    data.router,
    dependencies=[Depends(store_shard)],
)


class SimplifiedEmbedToken(BaseModel):
    token: str
    expiration_date: str
    dataset_id: str
    version: VersionId


@router.get(
    "/dashboards-auth-token",
    response_model=SimplifiedEmbedToken,
    tags=[ApiTag.PUBLIC],
)
def get_dashboards_auth_token(
    settings: SettingsDependency,
    lifetime_in_minutes: int = 0,
):
    token, expiration = generate_simple_embed_token(
        dataset_ids=[settings.demo_mode.dateset_id],
        reports_ids=settings.demo_mode.reports_ids,
        lifetime_in_minutes=lifetime_in_minutes,
    )
    return SimplifiedEmbedToken(
        token=token,
        expiration_date=expiration,
        dataset_id=settings.demo_mode.dateset_id,
        version=settings.demo_mode.version,
    )


@router.get("/desktop-definitions", tags=[ApiTag.PUBLIC])
def get_desktop_definitions(settings: SettingsDependency):
    desktop_definitions = json.loads(settings.demo_mode.desktop_definitions.read_text())
    return JSONResponse(content=jsonable_encoder(desktop_definitions))
