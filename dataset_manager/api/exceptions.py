import logging
from contextlib import suppress
from typing import Any

import requests
from fastapi import Request
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from pydantic import ValidationError
from starlette.status import (
    HTTP_202_ACCEPTED,
    HTTP_404_NOT_FOUND,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_503_SERVICE_UNAVAILABLE,
)

from dataset_manager.azure.exceptions import PBIError
from dataset_manager.azure.powerbi.datasets import RefreshRequestAlreadyExecuting
from dataset_manager.connectors.user_service import (
    UserNotFound,
    UserServiceNotAvailable,
)
from dataset_manager.logic.version import FrozenVersionAlreadyCreated
from dataset_manager.repo.capacity import CapacityNotFound
from dataset_manager.repo.profile import ProfileNotFound
from dataset_manager.repo.shard import ShardNotFound
from dataset_manager.repo.version import VersionNotFound

log = logging.getLogger(__name__)


def _http_error_response(
    status_code: int, detail: Any | None = None, content: Any | None = None
) -> JSONResponse:
    content = {"detail": detail} if detail else content
    assert content is not None
    return JSONResponse(status_code=status_code, content=content)


http_studio_not_found = _http_error_response(HTTP_404_NOT_FOUND, "Studio not found")
http_version_not_found = _http_error_response(HTTP_404_NOT_FOUND, "Version not found")
http_shard_refresh_already_executing = _http_error_response(
    HTTP_202_ACCEPTED, "Another refresh request is already executing"
)
http_capacity_not_found = _http_error_response(HTTP_404_NOT_FOUND, "Capacity not found")
http_frozen_version_already_created = _http_error_response(
    HTTP_409_CONFLICT, "Frozen version already created"
)

http_user_service_unavailable = _http_error_response(
    HTTP_503_SERVICE_UNAVAILABLE, "User Service not available"
)


async def handle_domain_exceptions(request: Request, call_next) -> Any:
    try:
        return await call_next(request)
    except ProfileNotFound:
        return http_studio_not_found
    except ShardNotFound:
        return http_studio_not_found
    except UserNotFound:
        return http_studio_not_found
    except VersionNotFound:
        return http_version_not_found
    except FrozenVersionAlreadyCreated:
        return http_frozen_version_already_created
    except CapacityNotFound:
        return http_capacity_not_found
    except RefreshRequestAlreadyExecuting:
        return http_shard_refresh_already_executing
    except UserServiceNotAvailable:
        return http_user_service_unavailable
    except PBIError as e:
        return _http_error_response(
            HTTP_409_CONFLICT,
            content={"detail": e.detail, "error_code": e.PBI_ERROR_CODE},
        )
    except ValidationError as e:
        return _http_error_response(
            HTTP_422_UNPROCESSABLE_ENTITY, jsonable_encoder(e.errors())
        )
    except requests.RequestException as e:
        response_message = "Unknown"
        with suppress(Exception):
            if e.response is not None:
                response_message = e.response.text
        log.error(
            "While calling %s there was an error %s with response: %s",
            request.url,
            e,
            response_message,
        )

        return _http_error_response(
            HTTP_503_SERVICE_UNAVAILABLE, "Oops! Error calling external services"
        )
