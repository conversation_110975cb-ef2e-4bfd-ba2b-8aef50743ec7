from fastapi import APIRouter, Depends
from pydantic import BaseModel
from sqlalchemy.orm import Session

from dataset_manager.api.api_tags import ApiTag
from dataset_manager.api.dependencies import get_capacieties_api_dependency
from dataset_manager.azure.management.capacities import CapacietiesAPI
from dataset_manager.connectors.db_engine import get_db
from dataset_manager.logic.capacity import (
    add_capacity,
    pause_capacity,
    resume_capacity,
    scale_down_capacity,
    scale_up_capacity,
    set_default_capacity,
    set_default_capacity_for_releases,
)
from dataset_manager.repo.capacity import (
    Capacity,
    CapacityName,
    CapacityRepo,
)

router = APIRouter(prefix="/capacity")


class CapacityCreate(BaseModel):
    capacity_name: CapacityName


@router.get("", response_model=list[Capacity], tags=[ApiTag.PRIVATE])
def list_capacities(session: Session = Depends(get_db)):
    return CapacityRepo(session=session).all()


@router.post("", response_model=Capacity, tags=[ApiTag.PRIVATE])
def add(
    capacity: CapacityCreate,
    session: Session = Depends(get_db),
    capacieties_api: CapacietiesAPI = Depends(get_capacieties_api_dependency),
):
    return add_capacity(session, capacity.capacity_name, capacieties_api)


@router.put("/{capacity_name}/scale_up", response_model=Capacity, tags=[ApiTag.PRIVATE])
def scale_up(
    capacity_name: CapacityName,
    session: Session = Depends(get_db),
    capacieties_api: CapacietiesAPI = Depends(get_capacieties_api_dependency),
):
    return scale_up_capacity(session, capacity_name, capacieties_api)


@router.put(
    "/{capacity_name}/scale_down", response_model=Capacity, tags=[ApiTag.PRIVATE]
)
def scale_down(
    capacity_name: CapacityName,
    session: Session = Depends(get_db),
    capacieties_api: CapacietiesAPI = Depends(get_capacieties_api_dependency),
):
    return scale_down_capacity(session, capacity_name, capacieties_api)


@router.put("/{capacity_name}/pause", response_model=Capacity, tags=[ApiTag.PRIVATE])
def pause(
    capacity_name: CapacityName,
    session: Session = Depends(get_db),
    capacieties_api: CapacietiesAPI = Depends(get_capacieties_api_dependency),
):
    capacity = CapacityRepo(session=session).get_by_name(capacity_name)
    return pause_capacity(session, capacity, capacieties_api)


@router.put("/{capacity_name}/resume", response_model=Capacity, tags=[ApiTag.PRIVATE])
def resume(
    capacity_name: CapacityName,
    session: Session = Depends(get_db),
    capacieties_api: CapacietiesAPI = Depends(get_capacieties_api_dependency),
):
    capacity = CapacityRepo(session=session).get_by_name(capacity_name)
    return resume_capacity(session, capacity, capacieties_api)


@router.put(
    "/{capacity_name}/set_default", response_model=Capacity, tags=[ApiTag.PRIVATE]
)
def set_default(capacity_name: CapacityName, session: Session = Depends(get_db)):
    return set_default_capacity(session, capacity_name)


@router.put(
    "/{capacity_name}/set_default_for_releases",
    response_model=Capacity,
    tags=[ApiTag.PRIVATE],
)
def set_default_for_releases(
    capacity_name: CapacityName, session: Session = Depends(get_db)
):
    return set_default_capacity_for_releases(session, capacity_name)


@router.delete("/{capacity_name}", response_model=None, tags=[ApiTag.PRIVATE])
def delete(
    capacity_name: CapacityName,
    session: Session = Depends(get_db),
    capacieties_api: CapacietiesAPI = Depends(get_capacieties_api_dependency),
):
    capacity = CapacityRepo(session=session).get_by_name(capacity_name)
    pause_capacity(session, capacity, capacieties_api)
    CapacityRepo(session=session).delete(capacity_name)
