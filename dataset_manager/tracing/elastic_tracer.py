import contextlib

from elasticapm import get_client
from elasticapm.conf.constants import ERROR
from elasticapm.processors import for_events
from elasticapm.utils.disttracing import TraceParent


@contextlib.contextmanager
def transaction(
    transaction_type: str, name: str, trace_parent_header: str | None = None
):
    client = get_client()
    if client is None:
        yield
    else:
        yield (
            client.begin_transaction(
                transaction_type=transaction_type,
                trace_parent=TraceParent.from_string(trace_parent_header),
            )
            if trace_parent_header
            else client.begin_transaction(transaction_type=transaction_type)
        )
        client.end_transaction(name=name, result="success")


@for_events(ERROR)
def remove_x_api_key_from_stacktrace_headers(_client, event):
    if "exception" in event and "stacktrace" in event["exception"]:
        for frame in event["exception"]["stacktrace"]:
            if (
                "vars" in frame
                and "scope" in frame["vars"]
                and "headers" in frame["vars"]["scope"]
            ):
                frame["vars"]["scope"]["headers"] = [
                    header
                    for header in frame["vars"]["scope"]["headers"]
                    if header[0] != b"x-api-key"
                ]
    return event
