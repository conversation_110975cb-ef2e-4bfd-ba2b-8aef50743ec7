import logging
from typing import Callable

import httpx
import requests

from dataset_manager.azure.exceptions import get_occured_error
from dataset_manager.azure.identity import aio_power_bi_credential, power_bi_credential
from dataset_manager.requests import send_request_with_retry

log = logging.getLogger(__name__)


def auth_token_provider(*scope) -> str:
    my_token = power_bi_credential.get_token(*scope).token
    return f"Bearer {my_token}"


async def aio_auth_token_provider(*scope) -> str:
    response = await aio_power_bi_credential.get_token(*scope)
    return f"Bearer {response.token}"


class AzureAPIClient:
    def __init__(self, auth_token_provider: Callable) -> None:
        self._auth_token_provider: Callable = auth_token_provider

    def get(self, url: str, **kwargs) -> requests.Response:
        return self.request("get", url=url, **kwargs)

    def post(self, url: str, **kwargs) -> requests.Response:
        return self.request("post", url=url, **kwargs)

    def put(self, url: str, **kwargs) -> requests.Response:
        return self.request("put", url=url, **kwargs)

    def patch(self, url: str, **kwargs) -> requests.Response:
        return self.request("patch", url=url, **kwargs)

    def delete(self, url: str, **kwargs) -> requests.Response:
        return self.request("delete", url=url, **kwargs)

    def request(self, method: str, url: str, **kwargs) -> requests.Response:
        log.info("Running %s, %s", method, url)
        headers = {
            "Authorization": self._auth_token_provider(),
            "Content-Type": "application/json",
        }

        if "headers" in kwargs:
            headers.update(kwargs["headers"])
            del kwargs["headers"]

        try:
            return send_request_with_retry(
                method=method, url=url, headers=headers, timeout=600, **kwargs
            )
        except requests.HTTPError as e:
            error_cls = get_occured_error(e)
            raise error_cls() from e


class AioAzureAPIClient:
    def __init__(self, auth_token_provider: Callable) -> None:
        self._auth_token_provider: Callable = auth_token_provider

    async def get(self, url: str, **kwargs) -> httpx.Response:
        return await self.request("get", url=url, **kwargs)

    async def post(self, url: str, **kwargs) -> httpx.Response:
        return await self.request("post", url=url, **kwargs)

    async def put(self, url: str, **kwargs) -> httpx.Response:
        return await self.request("put", url=url, **kwargs)

    async def patch(self, url: str, **kwargs) -> httpx.Response:
        return await self.request("patch", url=url, **kwargs)

    async def delete(self, url: str, **kwargs) -> httpx.Response:
        return await self.request("delete", url=url, **kwargs)

    async def request(self, method: str, url: str, **kwargs) -> httpx.Response:
        log.info("Running %s, %s", method, url)
        headers = {
            "Authorization": await self._auth_token_provider(),
            "Content-Type": "application/json",
        }

        if "headers" in kwargs:
            headers.update(kwargs["headers"])
            del kwargs["headers"]

        try:
            async with httpx.AsyncClient() as client:
                response = await client.request(
                    method=method, url=url, headers=headers, timeout=600, **kwargs
                )
                response.raise_for_status()
                return response
        except httpx.HTTPStatusError as e:
            error_cls = get_occured_error(e)
            raise error_cls() from e
