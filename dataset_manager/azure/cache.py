import functools
import logging
from typing import Any, Awaitable, Callable, Concatenate, ParamSpec, TypeVar

R = TypeVar("R")
P = ParamSpec("P")

log = logging.getLogger(__name__)


class DaxCache:
    def __init__(self) -> None:
        self._entries: dict[str, dict[Any, Any]] = {}

    def __call__(
        self, f: Callable[Concatenate[Any, str, P], Awaitable[R]]
    ) -> Callable[Concatenate[Any, str, P], Awaitable[R]]:
        @functools.wraps(f)  # type: ignore
        async def wrapper(_self: Any, key: str, *args: P.args, **kwargs: P.kwargs) -> R:
            keyed_entries = self._entries.setdefault(key, {})

            args_key = tuple(args)
            kwargs_key = tuple(kwargs.items())
            complete_key = (args_key, kwargs_key)

            if complete_key not in keyed_entries:
                log.info("Refreshing cache for key: %s and args: %s", key, complete_key)
                keyed_entries[complete_key] = await f(_self, key, *args, **kwargs)

            return keyed_entries[complete_key]

        return wrapper  # type: ignore

    def reset(self, key: str):
        if key in self._entries:
            log.info("Reset cache for key: %s", key)
            del self._entries[key]

    def reset_all(self):
        log.info("Reset cache for all")
        self._entries.clear()


cache = DaxCache()
