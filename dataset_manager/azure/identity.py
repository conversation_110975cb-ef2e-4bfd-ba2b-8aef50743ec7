import os

from azure.identity import ClientSecretCredential, DefaultAzureCredential
from azure.identity.aio import ClientSecretCredential as AioClientSecretCredential
from azure.identity.aio import DefaultAzureCredential as AioDefaultAzureCredential

credential = DefaultAzureCredential(logging_enable=True)
aio_credential = AioDefaultAzureCredential(logging_enable=True)

if os.environ.get("PBI_AZURE_TENANT_ID"):
    power_bi_credential: ClientSecretCredential | DefaultAzureCredential = (
        ClientSecretCredential(
            tenant_id=os.environ["PBI_AZURE_TENANT_ID"],
            client_id=os.environ["PBI_AZURE_CLIENT_ID"],
            client_secret=os.environ["PBI_AZURE_CLIENT_SECRET"],
        )
    )
    aio_power_bi_credential: AioClientSecretCredential | AioDefaultAzureCredential = (
        AioClientSecretCredential(
            tenant_id=os.environ["PBI_AZURE_TENANT_ID"],
            client_id=os.environ["PBI_AZURE_CLIENT_ID"],
            client_secret=os.environ["PBI_AZURE_CLIENT_SECRET"],
        )
    )
else:
    power_bi_credential = credential
    aio_power_bi_credential = aio_credential
