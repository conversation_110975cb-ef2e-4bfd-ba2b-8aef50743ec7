from azure.storage.blob import BlobServiceClient

MODELS_CONTAINER_NAME = "models"


class AzureStorageContainer:
    def __init__(self, client: BlobServiceClient, container_name: str) -> None:
        self._client = client.get_container_client(container=container_name)

    def download(self, name: str) -> bytes:
        return self._client.download_blob(name).readall()

    def upload(self, name: str, data: bytes):
        self._client.upload_blob(name=name, data=data, overwrite=True)


class ModelContainer(AzureStorageContainer):
    def __init__(
        self, client: BlobServiceClient, container_name: str = MODELS_CONTAINER_NAME
    ) -> None:
        super().__init__(client, container_name=container_name)
