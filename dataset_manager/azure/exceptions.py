import logging

import httpx
import requests

log = logging.getLogger(__name__)


class PBIError(Exception):
    PBI_ERROR_CODE: str = "PBI_UNKNOWN_ERROR"
    DEFAULT_MESSAGE = "Unknown PowerBI error"

    def __init__(self, message: str | None = None) -> None:
        self.detail = message if message else self.DEFAULT_MESSAGE
        super().__init__(self.detail)

    @classmethod
    def is_occurred(cls, error: requests.HTTPError) -> bool:
        return False


def get_occured_error(
    error: requests.HTTPError | httpx.HTTPStatusError,
) -> type[PBIError]:
    def get_all_subclasses(cls):
        all_subclasses = []
        for subclass in cls.__subclasses__():
            all_subclasses.append(subclass)
            all_subclasses.extend(get_all_subclasses(subclass))
        return all_subclasses

    for error_cls in get_all_subclasses(PBIError):
        if error_cls.is_occurred(error):
            return error_cls
    log.error(
        "Unsuported PBI API error. Status code: %s, body: %s",
        error.response.status_code,
        error.response.text,
    )
    return PBIError


class PBIRefreshAlreadyRunning(PBIError):
    PBI_ERROR_CODE = "PBI_REFRESH_ALREAD_RUNNING"
    DEFAULT_MESSAGE = "Another refresh request is already executing"

    @classmethod
    def is_occurred(cls, error: requests.HTTPError) -> bool:
        data = error.response.json()
        refresh_request_already_executing = (
            "error" in data
            and data["error"]["code"] == "InvalidRequest"
            and data["error"]["message"]
            == "Invalid dataset refresh request. Another refresh request is already executing"
        )
        if refresh_request_already_executing:
            return True
        return False


class PBIDatasetExecuteQueriesError(PBIError):
    _ERROR_MESSAGE: str = ""

    @classmethod
    def is_occurred(cls, error: requests.HTTPError) -> bool:
        return cls._check_for_error_message_in_pbi_error_details(error.response)

    @classmethod
    def _check_for_error_message_in_pbi_error_details(
        cls, response: requests.Response
    ) -> bool:
        data = response.json()

        if (
            "error" in data
            and data["error"]["code"] == "DatasetExecuteQueriesError"
            and "pbi.error" in data["error"]
            and "details" in data["error"]["pbi.error"]
        ):
            for detail in data["error"]["pbi.error"]["details"]:
                if (
                    "detail" in detail
                    and "value" in detail["detail"]
                    and detail["detail"]["value"] == cls._ERROR_MESSAGE
                ):
                    return True
        return False


_PBI_INSTANCE_OVERLOADED = "PBI_INSTANCE_OVERLOADED"


class PBICanceledCPULimit(PBIDatasetExecuteQueriesError):
    PBI_ERROR_CODE = _PBI_INSTANCE_OVERLOADED

    _ERROR_MESSAGE = "The database was evicted and the operation cancelled to load balance the CPU load on the node. Please try again later."
    DEFAULT_MESSAGE = _ERROR_MESSAGE


class PBIFabicCapacityExceeded(PBIDatasetExecuteQueriesError):
    PBI_ERROR_CODE = _PBI_INSTANCE_OVERLOADED

    _ERROR_MESSAGE = "Unable to complete the action because your organization's Fabric compute capacity has exceeded its limits. Try again later. See https://go.microsoft.com/fwlink/?linkid=2247526 to learn more."
    DEFAULT_MESSAGE = _ERROR_MESSAGE


class PBIConnectionFailed(PBIDatasetExecuteQueriesError):
    PBI_ERROR_CODE = "PBI_CANT_CONNECT_INSTANCE"

    _ERROR_MESSAGE = "Failed to open the MSOLAP connection."
    DEFAULT_MESSAGE = _ERROR_MESSAGE
