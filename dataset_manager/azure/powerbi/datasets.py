import json
import logging
import time
from datetime import datetime
from enum import Enum
from pathlib import Path
from time import sleep

from dateutil.parser import parse
from pydantic import BaseModel
from requests_toolbelt import MultipartEncoder

from dataset_manager.azure.cache import cache
from dataset_manager.azure.client import AioAzureAPIClient, AzureAPIClient
from dataset_manager.azure.exceptions import PBIRefreshAlreadyRunning
from dataset_manager.entities import DatasetId, ProcessedAdlsURL, WorkspaceId
from dataset_manager.repo.permission_set import PermissionSet

log = logging.getLogger(__name__)

_FIVE_MINUTES = 60 * 5


class PBIRefreshStatus(Enum):
    """https://docs.microsoft.com/en-us/rest/api/power-bi/datasets/get-refresh-history"""

    UNKNOWN = "Unknown"
    COMPLETED = "Completed"
    FAILED = "Failed"
    CANCELLED = "Cancelled"
    DISABLED = "Disabled"
    MISSING = "Missing"

    def is_completed(self) -> bool:
        return self == self.COMPLETED

    def is_refreshing(self) -> bool:
        return self == self.UNKNOWN

    def is_missing(self) -> bool:
        return self == self.MISSING

    def is_failed(self) -> bool:
        return self in (
            PBIRefreshStatus.FAILED,
            PBIRefreshStatus.DISABLED,
            PBIRefreshStatus.CANCELLED,
        )


class PBIRefresh(BaseModel):
    status: PBIRefreshStatus
    end_time: datetime | None = None
    details: dict | None = None


class DatasetDeployTimeout(Exception):
    def __init__(self, import_state: str, timeout_seconds: int) -> None:
        super().__init__(
            f"Dataset deploy failed with status {import_state} after {timeout_seconds}s"
        )


class ImportId(str):
    pass


class RefreshRequestAlreadyExecuting(Exception):
    pass


class DatasetNotFound(Exception):
    pass


class DatasetAPI:
    def __init__(
        self,
        azure_api_client: AzureAPIClient,
        deploy_timeout_seconds: int = _FIVE_MINUTES,
    ) -> None:
        self._api: AzureAPIClient = azure_api_client
        self._base_url: str = "https://api.powerbi.com/v1.0/myorg/groups"
        self._deploy_timeout_seconds: int = deploy_timeout_seconds

    def deploy(self, workspace_id: WorkspaceId, dataset_pbix: bytes) -> DatasetId:
        timestamp = int(time.time())
        imports_id = self._import_pbix(
            workspace_id=workspace_id,
            display_name=f"Dataset{timestamp}",
            pbix=dataset_pbix,
        )
        return self._get_dataset_id(import_id=imports_id, workspace_id=workspace_id)

    def deploy_from_file(
        self, workspace_id: WorkspaceId, dataset_path: Path
    ) -> DatasetId:
        with dataset_path.open("rb") as dataset_f:
            dataset_pbix = dataset_f.read()

        return self.deploy(workspace_id, dataset_pbix)

    def take_ownership(self, workspace_id: WorkspaceId, dataset_id: DatasetId) -> None:
        self._api.post(
            f"{self._base_url}/{workspace_id}/datasets/{dataset_id}/Default.TakeOver"
        )

    def set_permission_set(
        self,
        workspace_id: WorkspaceId,
        dataset_id: DatasetId,
        permission_set: PermissionSet,
    ):
        self._set_parameter(
            workspace_id=workspace_id,
            dataset_id=dataset_id,
            name="permission_set",
            value=permission_set.permission_set_json,
        )

    def get_permission_set(self, workspace_id: WorkspaceId, dataset_id: DatasetId):
        return self._get_parameter(
            workspace_id=workspace_id,
            dataset_id=dataset_id,
            name="permission_set",
        )

    def set_adls_url(
        self,
        workspace_id: WorkspaceId,
        dataset_id: DatasetId,
        processed_adls_url: ProcessedAdlsURL,
    ) -> None:
        self._set_parameter(
            workspace_id=workspace_id,
            dataset_id=dataset_id,
            name="URL_ROOT",
            value=processed_adls_url,
        )

    def get_adls_url(self, workspace_id: WorkspaceId, dataset_id: DatasetId):
        return self._get_parameter(
            workspace_id=workspace_id,
            dataset_id=dataset_id,
            name="URL_ROOT",
        )

    def _set_parameter(
        self, workspace_id: WorkspaceId, dataset_id: DatasetId, name: str, value: str
    ):
        self._api.post(
            f"{self._base_url}/{workspace_id}/datasets/{dataset_id}/Default.UpdateParameters",
            json={"updateDetails": [{"name": name, "newValue": value}]},
        )

    def _get_parameter(
        self, workspace_id: WorkspaceId, dataset_id: DatasetId, name: str
    ) -> str:
        response = self._api.get(
            f"{self._base_url}/{workspace_id}/datasets/{dataset_id}/parameters",
        ).json()["value"]
        return [x for x in response if x["name"] == name][0]["currentValue"]

    def set_credentials(
        self,
        workspace_id: WorkspaceId,
        dataset_id: DatasetId,
        processed_adls_credential: str,
    ) -> None:
        datasources = self._get_datasources(workspace_id, dataset_id)
        ds_object = datasources[0]
        data = {
            "credentialDetails": {
                "credentialType": "Key",
                "credentials": '{"credentialData":[{"name":"key", "value":"'
                + processed_adls_credential
                + '"}]}',
                "encryptedConnection": "Encrypted",
                "encryptionAlgorithm": "None",
                "privacyLevel": "Organizational",
            }
        }
        self._api.patch(
            f"https://api.powerbi.com/v1.0/myorg/gateways/{ds_object['gatewayId']}/datasources/{ds_object['datasourceId']}",
            json=data,
        )

    def start_refresh(
        self,
        workspace_id: WorkspaceId,
        dataset_id: DatasetId,
        allow_incremental: bool = False,
    ) -> None:
        try:
            self._api.post(
                f"{self._base_url}/{workspace_id}/datasets/{dataset_id}/refreshes",
                json={
                    "type": "full",
                    "applyRefreshPolicy": allow_incremental,
                },
            )
        except PBIRefreshAlreadyRunning:
            log.warning(f"Workspace {workspace_id} is already being refreshed")
            raise RefreshRequestAlreadyExecuting

    def start_refresh_on_create_shard(
        self,
        workspace_id: WorkspaceId,
        dataset_id: DatasetId,
    ) -> None:
        self._api.post(
            f"{self._base_url}/{workspace_id}/datasets/{dataset_id}/refreshes",
            json={"refresh": {"objects": [{"database": "Dataset"}], "type": "full"}},
        )

    def last_refresh(
        self, workspace_id: WorkspaceId, dataset_id: DatasetId
    ) -> PBIRefresh:
        response = self._api.get(
            f"{self._base_url}/{workspace_id}/datasets/{dataset_id}/refreshes?$top=1",
        )
        response.raise_for_status()

        last_refresh: list[dict] = response.json()["value"]
        if len(last_refresh) == 0:
            return PBIRefresh(status=PBIRefreshStatus.MISSING)
        return PBIRefresh(
            status=PBIRefreshStatus(last_refresh[0]["status"]),
            end_time=(
                parse(last_refresh[0]["endTime"])
                if "endTime" in last_refresh[0]
                else None
            ),
            details=(
                json.loads(last_refresh[0]["serviceExceptionJson"])
                if "serviceExceptionJson" in last_refresh[0]
                else None
            ),
        )

    def all_refreshes(self, workspace_id: WorkspaceId, dataset_id: DatasetId) -> list:
        response = self._api.get(
            f"{self._base_url}/{workspace_id}/datasets/{dataset_id}/refreshes",
        )
        response.raise_for_status()
        return response.json()

    def last_successful_refresh(
        self, workspace_id: WorkspaceId, dataset_id: DatasetId
    ) -> datetime | None:
        response = self._api.get(
            f"{self._base_url}/{workspace_id}/datasets/{dataset_id}/refreshes",
        )
        response.raise_for_status()
        last_successful_refresh = next(
            (
                refresh
                for refresh in response.json()["value"]
                if refresh["status"] == PBIRefreshStatus.COMPLETED.value
            ),
            None,
        )
        if last_successful_refresh is None:
            return None
        return parse(last_successful_refresh["endTime"])

    def get_dataset(self, workspace_id: str, dataset_name: str) -> DatasetId:
        response = self._api.get(
            f"https://api.powerbi.com/v1.0/myorg/groups/{workspace_id}/datasets"
        )
        for dataset in response.json()["value"]:
            if dataset["name"] == dataset_name:
                return DatasetId(dataset["id"])

        raise DatasetNotFound()

    def _get_datasources(
        self, workspace_id: WorkspaceId, dataset_id: DatasetId
    ) -> dict:
        response = self._api.get(
            f"{self._base_url}/{workspace_id}/datasets/{dataset_id}/datasources",
        )
        return response.json()["value"]

    def _import_pbix(
        self,
        workspace_id: WorkspaceId,
        display_name: str,
        pbix: bytes,
    ) -> ImportId:
        url = f"{self._base_url}/{workspace_id}/imports?datasetDisplayName={display_name}&nameConflict=CreateOrOverwrite"
        encoder = MultipartEncoder({"value": ("value", pbix)})

        response = self._api.post(
            url,
            data=encoder.to_string(),
            headers={"Content-Type": encoder.content_type},
        )
        return ImportId(response.json()["id"])

    def _get_dataset_id(
        self, import_id: ImportId, workspace_id: WorkspaceId
    ) -> DatasetId:
        url = f"{self._base_url}/{workspace_id}/imports/{import_id}"
        response = self._api.get(url)

        timeout = time.time() + self._deploy_timeout_seconds
        while response.json()["importState"] != "Succeeded":
            if time.time() > timeout:
                raise DatasetDeployTimeout(
                    import_state=response.json()["importState"],
                    timeout_seconds=self._deploy_timeout_seconds,
                )
            sleep(5)
            response = self._api.get(url)
        return DatasetId(response.json()["datasets"][0]["id"])


class AioDatasetAPI:
    def __init__(
        self,
        azure_api_client: AioAzureAPIClient,
        deploy_timeout_seconds: int = _FIVE_MINUTES,
    ) -> None:
        self._api: AioAzureAPIClient = azure_api_client
        self._base_url: str = "https://api.powerbi.com/v1.0/myorg/groups"
        self._deploy_timeout_seconds: int = deploy_timeout_seconds

    @cache
    async def execute_dax(
        self, workspace_id: str, dataset_id: str, dax_query: str
    ) -> list[dict]:
        response = await self._api.post(
            f"https://api.powerbi.com/v1.0/myorg/groups/{workspace_id}/datasets/{dataset_id}/executeQueries",
            json={
                "queries": [{"query": dax_query}],
                "serializerSettings": {"includeNulls": True},
            },
        )
        return response.json()["results"][0]["tables"][0]["rows"]


def get_dataset_api(auth_token_provider) -> DatasetAPI:
    azure_api_client = AzureAPIClient(auth_token_provider=auth_token_provider)
    return DatasetAPI(azure_api_client=azure_api_client)


def aio_get_dataset_api(auth_token_provider) -> AioDatasetAPI:
    azure_api_client = AioAzureAPIClient(auth_token_provider=auth_token_provider)
    return AioDatasetAPI(azure_api_client=azure_api_client)
