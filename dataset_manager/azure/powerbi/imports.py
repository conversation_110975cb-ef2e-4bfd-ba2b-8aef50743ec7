import io
import json
from time import sleep
from zipfile import ZIP_DEFLATED, ZipFile

from requests_toolbelt import MultipartEncoder

from dataset_manager.azure.powerbi.service import request


def deploy_dataset_from_template_workspace(
    workspace_id: str,
    template_workspace_id: str,
) -> str:
    report_id = _get_dataset_template_report_id(template_workspace_id)
    dataset_pbix = _get_pbix(template_workspace_id, report_id)
    return deploy_dataset(workspace_id, dataset_pbix)


def deploy_dataset(workspace_id: str, dataset_pbix: bytes):
    imports_id = _import_pbix(
        workspace_id=workspace_id,
        display_name="Dataset",
        pbix=dataset_pbix,
    )
    return _get_dataset_id(import_id=imports_id, workspace_id=workspace_id)


def deploy_visual(
    workspace_id: str, display_name: str, dataset_id: str, visual_pbix: bytes
):
    visual_with_connection = _replace_connection_string(
        visual_pbix,
        dataset_id=dataset_id,
    )
    return _import_pbix(
        workspace_id=workspace_id,
        display_name=display_name,
        pbix=visual_with_connection,
    )


def _import_pbix(
    workspace_id: str,
    display_name: str,
    pbix: bytes,
) -> str:
    url = f"https://api.powerbi.com/v1.0/myorg/groups/{workspace_id}/"
    url += f"imports?datasetDisplayName={display_name}"
    url += "&nameConflict=CreateOrOverwrite"

    encoder = MultipartEncoder({"value": ("value", pbix)})

    response = request(
        "post",
        url,
        data=encoder.to_string(),
        headers={"Content-Type": encoder.content_type},
    )
    return response.json()["id"]


def _replace_connection_string(visual_pbix: bytes, dataset_id: str) -> bytes:
    connection_string = {
        "Version": 3,
        "Connections": [
            {
                "Name": "EntityDataSource",
                "ConnectionString": f'Data Source=pbiazure://api.powerbi.com;Initial Catalog={dataset_id};Identity Provider="https://login.microsoftonline.com/common, https://analysis.windows.net/powerbi/api, 7f67af8a-fedc-4b08-8b4e-37c4d127b6cf";Integrated Security=ClaimsToken',
                "ConnectionType": "pbiServiceLive",
                "PbiServiceModelId": 0,
                "PbiModelVirtualServerName": "sobe_wowvirtualserver",
                "PbiModelDatabaseName": dataset_id,
            }
        ],
    }
    visual_with_connection = io.BytesIO()
    with (
        ZipFile(io.BytesIO(visual_pbix)) as original,
        ZipFile(visual_with_connection, "w", compression=ZIP_DEFLATED) as modified,
    ):
        for original_file in original.infolist():
            if original_file.filename == "Connections":
                modified.writestr(original_file, json.dumps(connection_string))
            else:
                with original.open(original_file.filename) as f:
                    modified.writestr(original_file, f.read())
    return visual_with_connection.getvalue()


def _get_dataset_id(import_id: str, workspace_id: str) -> str:
    url = (
        f"https://api.powerbi.com/v1.0/myorg/groups/{workspace_id}/imports/{import_id}"
    )
    response = request("get", url)
    while response.json()["importState"] != "Succeeded":
        sleep(5)
        response = request("get", url)
    return response.json()["datasets"][0]["id"]


def _get_dataset_template_report_id(template_workspace_id: str) -> str:
    url = f"https://api.powerbi.com/v1.0/myorg/groups/{template_workspace_id}/reports"
    response = request("get", url)
    for report in response.json()["value"]:
        if report["name"] == "Dataset":
            return report["id"]
    raise Exception("Workspace does not contain Dataset report")


def _get_pbix(template_workspace_id: str, report_id: str) -> bytes:
    url = f"https://api.powerbi.com/v1.0/myorg/groups/{template_workspace_id}/reports/{report_id}/Export"
    response = request("get", url)
    return response.content
