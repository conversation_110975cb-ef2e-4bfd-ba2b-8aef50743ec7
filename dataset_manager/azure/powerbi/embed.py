from dataset_manager.azure.powerbi.service import request


def generate_embed_token(
    dataset_ids: list[str],
    profile_id: str,
    reports_ids: list[str],
    lifetime_in_minutes: int = 0,
) -> tuple[str, str]:
    response = request(
        "post",
        "https://api.powerbi.com/v1.0/myorg/GenerateToken",
        headers={
            "X-PowerBI-profile-id": profile_id,
        },
        json={
            "datasets": [{"id": dataset_id} for dataset_id in dataset_ids],
            "reports": [{"id": report_id} for report_id in reports_ids],
            "lifetimeInMinutes": lifetime_in_minutes,
        },
    )
    token = response.json()
    return token["token"], token["expiration"]


def generate_simple_embed_token(
    dataset_ids: list[str],
    reports_ids: list[str],
    lifetime_in_minutes: int = 0,
) -> tuple[str, str]:
    response = request(
        "post",
        "https://api.powerbi.com/v1.0/myorg/GenerateToken",
        json={
            "datasets": [{"id": dataset_id} for dataset_id in dataset_ids],
            "reports": [{"id": report_id} for report_id in reports_ids],
            "lifetimeInMinutes": lifetime_in_minutes,
        },
    )
    token = response.json()
    return token["token"], token["expiration"]
