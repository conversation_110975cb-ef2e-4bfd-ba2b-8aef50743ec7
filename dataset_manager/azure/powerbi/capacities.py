from dataset_manager.azure.powerbi.service import request
from dataset_manager.entities import CapacityId
from dataset_manager.repo.capacity import CapacityName, CapacityState


def _get_all_capacities() -> list[dict]:
    response = request("get", "https://api.powerbi.com/v1.0/myorg/capacities")
    return response.json()["value"]


def get_id_by_name(c_name: CapacityName) -> CapacityId:
    capacities = _get_all_capacities()
    for capacity in capacities:
        if capacity["displayName"] == c_name:
            return CapacityId(capacity["id"])
    raise Exception(f"Capacity {c_name} not found")


def get_state_by_name(c_name: CapacityName) -> CapacityState:
    capacities = _get_all_capacities()
    for capacity in capacities:
        if capacity["displayName"] == c_name:
            return CapacityState.from_raw(capacity["state"])
    raise Exception(f"Capacity {c_name} not found")
