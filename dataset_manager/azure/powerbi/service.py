import logging

from dataset_manager.azure.identity import power_bi_credential
from dataset_manager.config import PBI_TOKEN_URL
from dataset_manager.requests import send_request_with_retry

log = logging.getLogger(__name__)


def _get_client_token() -> str:
    my_token = power_bi_credential.get_token(PBI_TOKEN_URL).token
    return f"Bearer {my_token}"


def request(method: str, url: str, **kwargs):
    log.info("Running %s, %s", method, url)
    headers = {"Authorization": _get_client_token(), "Content-Type": "application/json"}

    if "headers" in kwargs:
        headers.update(kwargs["headers"])
        del kwargs["headers"]

    return send_request_with_retry(
        method=method, url=url, headers=headers, timeout=600, **kwargs
    )
