import contextlib
import logging
from collections import defaultdict

import sentry_sdk

from dataset_manager.azure.blob import ModelContainer
from dataset_manager.azure.management.capacities import CapacietiesAPI
from dataset_manager.azure.powerbi.datasets import DatasetAPI
from dataset_manager.azure.powerbi.workspaces import WorkspaceAPI
from dataset_manager.config import SERVICE_NAME
from dataset_manager.entities import (
    Environment,
    ProcessedAdlsCredentials,
    ProcessedAdlsURL,
    ServicePrincipalId,
)
from dataset_manager.logic.capacity import pause_capacity, resume_capacity
from dataset_manager.logic.shard import (
    AsignToProfileRequest,
    CreatePermissionSetShardRequest,
    RefreshTooHeavyPermissionSet,
    asign_to_profile,
    assing_to_best_capacity,
    create_permission_set_shard,
)
from dataset_manager.logs import RELEASE_CONTEXT_NAME, add_logging_context
from dataset_manager.repo import Repo
from dataset_manager.repo.release import Release, ReleaseStatus
from dataset_manager.repo.shard import ShardNotFound
from dataset_manager.tracing.elastic_tracer import transaction

log = logging.getLogger(__name__)


MAX_RELEASE_RETRIES = 3


def assign_releases(repo: Repo, capacieties_api: CapacietiesAPI):
    not_done_releases = repo.release.not_done()
    default_for_releases = repo.capacity.default_for_releases()

    if not_done_releases:
        resume_capacity(
            session=repo.capacity._session,  # TODO: pass repo at next refactoring batch
            capacity=default_for_releases,
            capacieties_api=capacieties_api,
            update_capacity=False,
        )
    else:
        pause_capacity(
            session=repo.capacity._session,  # TODO: pass repo at next refactoring batch
            capacity=default_for_releases,
            capacieties_api=capacieties_api,
            update_capacity=False,
        )

    releases_by_shard = defaultdict(list)
    for release in not_done_releases:
        key = f"{release.permission_set_uuid}_{release.version_id}"
        releases_by_shard[key].append(release)

    for releases_for_shard in releases_by_shard.values():
        release = releases_for_shard[0]
        if release.status != ReleaseStatus.REQUESTED:
            continue

        permission_set = repo.permission_set.get(release.permission_set_uuid)
        new_version = repo.version.get(release.version_id)

        new_release_shard = None
        with contextlib.suppress(ShardNotFound):
            new_release_shard = repo.shard.get_for_permission_set_and_version(
                permission_set=permission_set, version=new_version
            )

        if new_release_shard is None or (
            release.is_full_recreate
            and release.creation_timestamp > new_release_shard.creation_timestamp
        ):
            release.status = ReleaseStatus.REQUESTED_SHARD
            repo.release.save(release=release)
        else:
            for release_to_assign in releases_for_shard:
                release_to_assign.status = ReleaseStatus.REQUESTED_ASSIGN
                # TODO: Change to bulk assign
                repo.release.save(release=release_to_assign)


@transaction(transaction_type="function", name=f"{SERVICE_NAME}.handle_requested_asign")
def handle_requested_asign(
    repo: Repo,
    workspace_api: WorkspaceAPI,
    environment: Environment,
    service_principal_id: ServicePrincipalId,
    release: Release,
):
    with add_logging_context(RELEASE_CONTEXT_NAME, **release.model_dump()):
        try:
            log.info("Releasing %s: Asigning to profile", release.uuid)
            permission_set = repo.permission_set.get(release.permission_set_uuid)
            new_version = repo.version.get(release.version_id)
            new_release_shard = repo.shard.get_for_permission_set_and_version(
                permission_set=permission_set, version=new_version
            )
            asign_to_profile(
                repo=repo,
                workspace_api=workspace_api,
                request=AsignToProfileRequest(
                    workspaces=[
                        new_version.visuals_workspace_id,
                        new_version.shard_template_workspace_id,
                        new_release_shard.workspace_id,
                    ],
                    environment=environment,
                    studio_id=release.studio_id,
                    version_id=release.version_id,
                    permission_set_uuid=release.permission_set_uuid,
                    service_principal_id=service_principal_id,
                ),
            )
            release.status = ReleaseStatus.DONE
            repo.release.save(release=release)
            log.info("Releasing %s: Fully released", release.uuid)
        except RefreshTooHeavyPermissionSet as e:
            log.exception(
                "Release %s failed because of permission set %s is too heavy",
                release.uuid,
                release.permission_set_uuid,
            )
            log.exception("Marking release as failed: %s", release)
            sentry_sdk.capture_exception(e)
            release.try_count += 1
            release.status = ReleaseStatus.FAILED
            repo.release.save(release=release)

        except Exception as e:
            log.exception("Release failed because of %s", e)
            sentry_sdk.capture_exception(e)
            release.try_count += 1
            if release.try_count > MAX_RELEASE_RETRIES:
                log.exception("Marking release as failed: %s", release)
                release.status = ReleaseStatus.FAILED
            repo.release.save(release=release)


@transaction(transaction_type="function", name=f"{SERVICE_NAME}.handle_requested_shard")
def handle_requested_shard(
    repo: Repo,
    workspace_api: WorkspaceAPI,
    dataset_api: DatasetAPI,
    model_container: ModelContainer,
    environment: Environment,
    processed_adls_url: ProcessedAdlsURL,
    processed_adls_credentials: ProcessedAdlsCredentials,
    release: Release,
):
    with add_logging_context(RELEASE_CONTEXT_NAME, **release.model_dump()):
        try:
            log.info("Releasing %s: Creating shard for new version", release.uuid)
            new_version = repo.version.get(release.version_id)
            new_release_shard = create_permission_set_shard(
                repo=repo,
                workspace_api=workspace_api,
                dataset_api=dataset_api,
                model_container=model_container,
                request=CreatePermissionSetShardRequest(
                    permission_set_uuid=release.permission_set_uuid,
                    shard_version=new_version.shard_version,
                    environment=environment,
                    processed_adls_url=processed_adls_url,
                    processed_adls_credentials=processed_adls_credentials,
                ),
            )
            log.info(
                "Releasing %s: Shard finished refreshing. Movig to destination capacity",
                release.uuid,
            )
            assing_to_best_capacity(
                repo=repo, workspace_api=workspace_api, shard=new_release_shard
            )
            release.status = ReleaseStatus.REQUESTED_ASSIGN
            repo.release.save(release=release)
            log.info(
                "Releasing %s: Shard fully created. Requesting assign", release.uuid
            )
        except Exception as e:
            log.exception("Release failed because of %s", e)
            sentry_sdk.capture_exception(e)
            release.try_count += 1
            if release.try_count > MAX_RELEASE_RETRIES:
                log.exception("Marking release as failed: %s", release)
                release.status = ReleaseStatus.FAILED
            repo.release.save(release=release)
