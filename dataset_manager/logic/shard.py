import logging
import random
from datetime import datetime
from time import sleep

from pydantic import BaseModel
from sqlalchemy.orm import Session

from dataset_manager.azure.blob import ModelContainer
from dataset_manager.azure.powerbi.datasets import DatasetAPI
from dataset_manager.azure.powerbi.workspaces import Workspace<PERSON>I
from dataset_manager.config import SERVICE_NAME
from dataset_manager.entities import (
    Environment,
    PermissionSetUUID,
    ProcessedAdlsCredentials,
    ProcessedAdlsURL,
    ServicePrincipalId,
    ShardVersion,
    ShardWorkspaceId,
    StudioId,
    WorkspaceId,
    WorkspaceName,
)
from dataset_manager.logic.profile import create_profile
from dataset_manager.repo import Repo
from dataset_manager.repo.capacity import Capacity
from dataset_manager.repo.shard import Shard, ShardRepo
from dataset_manager.repo.version import VersionId
from dataset_manager.tracing.elastic_tracer import transaction

log = logging.getLogger(__name__)

_TOO_HEAVY_PERMISSION_SET_COUNTER_LIMIT = 240  # 240 * 30s = 120 minutes


class InvalidPermissionSetInPowerBI(Exception):
    def __init__(self, permission_set_in_pbi, expected_permission_set) -> None:
        super().__init__(
            f"Invalid permission set in powerBI. Got: {permission_set_in_pbi}, expected {expected_permission_set}",
        )


class InvalidAdlsURLInPowerBI(Exception):
    def __init__(self, adls_url_in_pbi, expected_adls_url) -> None:
        super().__init__(
            f"Invalid adls_url in powerBI. Got: {adls_url_in_pbi}, expected {expected_adls_url}",
        )


class RefreshFailed(Exception):
    pass


class RefreshTooHeavyPermissionSet(Exception):
    def __init__(self, permission_set_uuid) -> None:
        super().__init__(
            f"Release of permission_set_uuid: {permission_set_uuid} failed because it is too heavy to refresh.",
        )


class AsignToProfileRequest(BaseModel):
    workspaces: list[WorkspaceId]
    environment: Environment
    studio_id: StudioId
    version_id: VersionId
    permission_set_uuid: PermissionSetUUID
    service_principal_id: ServicePrincipalId


@transaction(transaction_type="function", name=f"{SERVICE_NAME}.asign_to_profile")
def asign_to_profile(
    repo: Repo, workspace_api: WorkspaceAPI, request: AsignToProfileRequest
):
    if repo.profile.exists_for_studio(studio_id=request.studio_id):
        profile = repo.profile.get_for_studio(studio_id=request.studio_id)
    else:
        profile = create_profile(
            profile_repo=repo.profile,
            environment=request.environment,
            studio_id=request.studio_id,
            active_version_id=request.version_id,
            permission_set_uuid=request.permission_set_uuid,
        )
    for workspace in request.workspaces:
        workspace_api.assign_profile(
            workspace,
            profile,
            service_principal_id=request.service_principal_id,
        )
    profile.active_version_id = request.version_id
    profile.permission_set_uuid = request.permission_set_uuid
    repo.profile.save(profile)


@transaction(
    transaction_type="function", name=f"{SERVICE_NAME}.assing_to_best_capacity"
)
def assing_to_best_capacity(
    repo: Repo, workspace_api: WorkspaceAPI, shard: Shard
) -> Shard:
    active_capacities = repo.capacity.active_not_for_releases()
    capacity_for_release = random.choice(active_capacities)
    workspace_api.assign_to_capacity(
        workspace_id=shard.workspace_id, capacity_id=capacity_for_release.id
    )
    shard = shard.model_copy(update={"capacity_id": capacity_for_release.id})
    return repo.shard.save(shard)


@transaction(transaction_type="function", name=f"{SERVICE_NAME}.assign_to_capacity")
def assign_to_capacity(
    repo: Repo, workspace_api: WorkspaceAPI, shard: Shard, capacity: Capacity
) -> Shard:
    workspace_api.assign_to_capacity(
        workspace_id=shard.workspace_id, capacity_id=capacity.id
    )
    shard = shard.model_copy(update={"capacity_id": capacity.id})
    return repo.shard.save(shard)


class CreatePermissionSetShardRequest(BaseModel):
    permission_set_uuid: PermissionSetUUID
    shard_version: ShardVersion
    environment: Environment
    processed_adls_url: ProcessedAdlsURL
    processed_adls_credentials: ProcessedAdlsCredentials

    @property
    def workspace_name(self) -> WorkspaceName:
        return WorkspaceName(
            f"{self.environment}_{self.shard_version}_{self.permission_set_uuid}"
        )


@transaction(
    transaction_type="function", name=f"{SERVICE_NAME}.create_permission_set_shard"
)
def create_permission_set_shard(
    repo: Repo,
    workspace_api: WorkspaceAPI,
    dataset_api: DatasetAPI,
    model_container: ModelContainer,
    request: CreatePermissionSetShardRequest,
) -> Shard:
    log.info("Create permission set shard %s", request.workspace_name)
    workspace_id: ShardWorkspaceId = ShardWorkspaceId(
        workspace_api.create(workspace_name=request.workspace_name)
    )
    capacity_for_release = repo.capacity.default_for_releases()
    workspace_api.assign_to_capacity(workspace_id, capacity_id=capacity_for_release.id)
    dataset_pbix = model_container.download(name=request.shard_version)
    dataset_id = dataset_api.deploy(workspace_id, dataset_pbix)
    permission_set = repo.permission_set.get(request.permission_set_uuid)
    dataset_api.take_ownership(workspace_id, dataset_id)
    dataset_api.set_permission_set(workspace_id, dataset_id, permission_set)
    permission_set_in_pbi = dataset_api.get_permission_set(
        workspace_id=workspace_id, dataset_id=dataset_id
    )
    if permission_set.permission_set_json != permission_set_in_pbi:
        raise InvalidPermissionSetInPowerBI(
            permission_set_in_pbi=permission_set_in_pbi,
            expected_permission_set=permission_set,
        )
    dataset_api.set_adls_url(
        workspace_id,
        dataset_id,
        processed_adls_url=request.processed_adls_url,
    )
    adls_url_in_pbi = dataset_api.get_adls_url(
        workspace_id=workspace_id, dataset_id=dataset_id
    )
    if request.processed_adls_url != adls_url_in_pbi:
        raise InvalidAdlsURLInPowerBI(
            adls_url_in_pbi=adls_url_in_pbi,
            expected_adls_url=request.processed_adls_url,
        )
    dataset_api.set_credentials(
        workspace_id,
        dataset_id,
        processed_adls_credential=request.processed_adls_credentials,
    )
    dataset_api.start_refresh_on_create_shard(workspace_id, dataset_id)
    missing_first_time = True
    too_heavy_permission_set_timeout_counter = 0
    counter = 0
    while True:
        last_refresh = dataset_api.last_refresh(workspace_id, dataset_id)
        if (
            too_heavy_permission_set_timeout_counter
            >= _TOO_HEAVY_PERMISSION_SET_COUNTER_LIMIT
        ):
            raise RefreshTooHeavyPermissionSet(
                permission_set_uuid=request.permission_set_uuid
            )

        if last_refresh.status.is_refreshing():
            log.info("Shard %s is still refreshing.", workspace_id)
            counter += 1
            too_heavy_permission_set_timeout_counter += 1
            if counter > 10:
                # run simple query to keep session alive
                permission_set = repo.permission_set.get(request.permission_set_uuid)
                counter = 0
            sleep(30)
        elif last_refresh.status.is_missing():
            log.warning("Shard %s refresh is missing!", workspace_id)
            if missing_first_time:
                missing_first_time = False
                sleep(5)
                continue
            dataset_api.start_refresh_on_create_shard(workspace_id, dataset_id)
        elif last_refresh.status.is_failed():
            log.warning(
                "Shard %s refresh Failed! Details :%s",
                workspace_id,
                last_refresh.details,
            )
            dataset_api.start_refresh_on_create_shard(workspace_id, dataset_id)
            raise RefreshFailed
        else:
            break
    shard_properties = Shard(
        version=request.shard_version,
        workspace_id=workspace_id,
        workspace_name=request.workspace_name,
        dataset_id=dataset_id,
        dataset_name=None,
        capacity_id=capacity_for_release.id,
        creation_timestamp=datetime.utcnow(),
        permission_set_uuid=permission_set.uuid,
        last_refresh_timestamp=last_refresh.end_time,
    )
    return repo.shard.save(shard_properties)


def fill_last_refresh_timestamp(
    session: Session, dataset_api: DatasetAPI, shard: Shard
) -> Shard:
    last_refresh_timestamp = dataset_api.last_successful_refresh(
        shard.workspace_id, shard.dataset_id
    )
    if last_refresh_timestamp:
        shard_repo = ShardRepo(session=session)
        shard = shard.model_copy(
            update={"last_refresh_timestamp": last_refresh_timestamp}
        )
        shard_repo.save(shard)
    return shard
