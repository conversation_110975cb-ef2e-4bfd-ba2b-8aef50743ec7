from __future__ import annotations

import logging
from enum import Enum
from time import sleep

from sqlalchemy.orm import Session

from dataset_manager.azure.management.capacities import CapacietiesAPI
from dataset_manager.azure.powerbi.capacities import get_id_by_name, get_state_by_name
from dataset_manager.repo.capacity import (
    Capacity,
    CapacityName,
    CapacityRepo,
    CapacityState,
    UniqueFlag,
)

log = logging.getLogger(__name__)

MAX_PBI_INSTANCE_TIER = "A6"
MAX_RETRIES_TO_SCALING_CAPACITY_SUCCESS = 4


class PBIEmbeddedCapacityState(str, Enum):
    SUCCEEDED = "Succeeded"
    UPDATING = "Updating"
    UNKNOWN = "Unknown"

    @classmethod
    def from_raw(cls, raw_state) -> PBIEmbeddedCapacityState:
        if raw_state == "Succeeded":
            return PBIEmbeddedCapacityState.SUCCEEDED
        log.warning("Capacity in unknown state, %s", raw_state)
        return PBIEmbeddedCapacityState.UNKNOWN


class CapacityScalingError(Exception):
    def __init__(self, capacity_name, tier) -> None:
        super().__init__(
            f"Capacity {capacity_name} not scaled to {tier}",
        )


def update_capacity_details(
    session: Session, capacity_name: CapacityName, capacieties_api: CapacietiesAPI
) -> Capacity:
    capacity = CapacityRepo(session=session).get_by_name(capacity_name)
    capacity_details = capacieties_api.details(capacity_name)
    capacity.state = CapacityState.ACTIVE
    if capacity_details["properties"]["state"] == "Succeeded":
        capacity.state = CapacityState.ACTIVE
    elif capacity_details["properties"]["state"] == "Paused":
        capacity.state = CapacityState.PAUSED
    else:
        capacity.state = CapacityState.UNKNOWN
    capacity.tier = capacity_details["sku"]["name"]
    return CapacityRepo(session=session).save(capacity)


def add_capacity(
    session: Session, capacity_name: CapacityName, capacieties_api: CapacietiesAPI
) -> Capacity:
    capacity_details = capacieties_api.details(capacity_name)
    capacity_id = get_id_by_name(capacity_name)
    return CapacityRepo(session).save(
        capacity=Capacity(
            id=capacity_id,
            name=capacity_name,
            tier=capacity_details["sku"]["name"],
            state=CapacityState.ACTIVE
            if capacity_details["properties"]["provisioningState"] == "Succeeded"
            else CapacityState.PAUSED,
            is_default=False,
            is_default_for_releases=False,
        )
    )


def scale_up_capacity(
    session: Session,
    capacity_name: CapacityName,
    capacieties_api: CapacietiesAPI,
) -> Capacity:
    capacity = update_capacity_details(
        session=session,
        capacity_name=capacity_name,
        capacieties_api=capacieties_api,
    )
    if capacity.tier != MAX_PBI_INSTANCE_TIER:
        new_tier = f"A{str(int(capacity.tier[-1]) + 1)}"
        log.info("Scaling up %s capacity to %s", capacity.name, new_tier)
        capacieties_api.scale(capacity.name, new_tier)
        capacity.tier = new_tier
        capacity = CapacityRepo(session=session).save(capacity)
    elif capacity.tier == MAX_PBI_INSTANCE_TIER:
        log.warning(
            "Capacity %s is already at %s tier, attempting to reset it...",
            capacity.name,
            MAX_PBI_INSTANCE_TIER,
        )
        capacity = _set_defined_capacity_tier(
            session=session,
            capacity=capacity,
            capacieties_api=capacieties_api,
            tier="A8",
        )
        capacity = _set_defined_capacity_tier(
            session=session,
            capacity=capacity,
            capacieties_api=capacieties_api,
            tier="A2",
        )

    return capacity


def scale_down_capacity(
    session: Session,
    capacity_name: CapacityName,
    capacieties_api: CapacietiesAPI,
) -> Capacity:
    capacity = update_capacity_details(
        session=session,
        capacity_name=capacity_name,
        capacieties_api=capacieties_api,
    )
    if capacity.tier != "A1":
        new_tier = f"A{str(int(capacity.tier[-1]) - 1)}"
        log.info("Scaling down %s capacity to %s", capacity.name, new_tier)
        capacieties_api.scale(capacity.name, new_tier)
        capacity.tier = new_tier
        capacity = CapacityRepo(session=session).save(capacity)
    return capacity


def pause_capacity(
    session: Session,
    capacity: Capacity,
    capacieties_api: CapacietiesAPI,
    update_capacity: bool = True,
) -> Capacity:
    if update_capacity:
        capacity = update_capacity_details(
            session=session,
            capacity_name=capacity.name,
            capacieties_api=capacieties_api,
        )
    if capacity.state != CapacityState.PAUSED:
        log.info("Pausing %s capacity", capacity.name)
        capacieties_api.pause(capacity.name)
        while capacity.state != CapacityState.PAUSED:
            sleep(5)
            capacity.state = get_state_by_name(capacity.name)

        log.info("Capacity %s succesfully paused", capacity.name)
        capacity = CapacityRepo(session=session).save(capacity)
    return capacity


def resume_capacity(
    session: Session,
    capacity: Capacity,
    capacieties_api: CapacietiesAPI,
    update_capacity: bool = True,
) -> Capacity:
    if update_capacity:
        capacity = update_capacity_details(
            session=session,
            capacity_name=capacity.name,
            capacieties_api=capacieties_api,
        )
    if capacity.state != CapacityState.ACTIVE:
        log.info("Resuming %s capacity", capacity.name)
        capacieties_api.resume(capacity.name)
        while capacity.state != CapacityState.ACTIVE:
            sleep(5)
            capacity.state = CapacityState(get_state_by_name(capacity.name))

        log.info("Capacity %s succesfully resumed", capacity.name)
        capacity = CapacityRepo(session=session).save(capacity)
    return capacity


def set_default_capacity(session: Session, capacity_name: CapacityName):
    return _set_unique_flag(session, capacity_name, flag=UniqueFlag.IS_DEFAULT)


def set_default_capacity_for_releases(session: Session, capacity_name: CapacityName):
    return _set_unique_flag(
        session, capacity_name, flag=UniqueFlag.IS_DEFAULT_FOR_RELEASE
    )


def _set_unique_flag(
    session: Session, capacity_name: CapacityName, flag: UniqueFlag
) -> Capacity:
    CapacityRepo(session=session).unset_flag_for_all(flag=flag)
    capacity = CapacityRepo(session=session).get_by_name(capacity_name)
    capacity.set_flag(flag=flag)
    return CapacityRepo(session=session).save(capacity)


def _get_pbi_embedded_capacity_state(
    capacity: Capacity, capacieties_api: CapacietiesAPI
) -> PBIEmbeddedCapacityState:
    state = PBIEmbeddedCapacityState.from_raw(
        capacieties_api.details(capacity.name)["properties"]["state"]
    )
    log.info("Capacity %s is in %s state", capacity.name, state)
    return state


def _set_defined_capacity_tier(
    session: Session, capacity: Capacity, capacieties_api: CapacietiesAPI, tier: str
) -> Capacity:
    capacieties_api.scale(capacity.name, tier)
    log.info("Setting %s capacity to %s", capacity.name, tier)

    for i in range(MAX_RETRIES_TO_SCALING_CAPACITY_SUCCESS):
        state = _get_pbi_embedded_capacity_state(capacity, capacieties_api)
        if state == PBIEmbeddedCapacityState.SUCCEEDED:
            log.info(
                "Capacity %s scaling success! Current tier: %s", capacity.name, tier
            )
            capacity.tier = tier
            capacity = CapacityRepo(session=session).save(capacity)
            return capacity
        log.info(
            "Waiting for capacity %s scaling success, current status: %s, try %i/4...",
            capacity.name,
            tier,
            i + 1,
        )
        sleep(10 * i)
    capacity.state = CapacityState.UNKNOWN
    capacity = CapacityRepo(session=session).save(capacity)
    raise CapacityScalingError(capacity.name, tier)
