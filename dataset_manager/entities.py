import base64
import uuid
from dataclasses import dataclass
from enum import Enum
from typing import Any, cast

from pydantic import (
    BaseModel,
    Field,
    GetCoreSchemaHandler,
    ValidationInfo,
    model_validator,
)
from pydantic_core import CoreSchema, core_schema


class BaseIntField(int):
    @classmethod
    def __get_pydantic_core_schema__(
        cls, source_type: Any, handler: GetCoreSchemaHandler
    ) -> CoreSchema:
        return core_schema.no_info_after_validator_function(cls, handler(int))


class BaseStrField(str):
    @classmethod
    def __get_pydantic_core_schema__(
        cls, source_type: Any, handler: GetCoreSchemaHandler
    ) -> CoreSchema:
        return core_schema.no_info_after_validator_function(cls, handler(str))


class BaseUUIDField(uuid.UUID):
    @classmethod
    def validate(cls, value, info: ValidationInfo):
        return cls(str(value))

    @classmethod
    def __get_pydantic_core_schema__(cls, source, handler) -> core_schema.CoreSchema:
        return core_schema.with_info_before_validator_function(
            cls.validate, handler(uuid.UUID), field_name=handler.field_name
        )


class StudioId(BaseIntField): ...


class OrgId(BaseStrField): ...


class Environment(BaseStrField): ...


class ServicePrincipalId(BaseStrField): ...


class ProcessedAdlsURL(BaseStrField): ...


class ProcessedAdlsCredentials(BaseStrField): ...


class WorkspaceId(BaseStrField): ...


class WorkspaceName(BaseStrField): ...


class CapacityId(BaseStrField): ...


class CapacityName(BaseStrField): ...


class VersionId(BaseStrField):
    @property
    def full_recreate_on_release(self):
        return self.endswith("_dev")


class ShardVersion(BaseStrField): ...


class ShardWorkspaceId(WorkspaceId): ...


class DatasetTemplateWorkspaceId(WorkspaceId): ...


class VisualseWorkspaceId(WorkspaceId): ...


class DatasetId(BaseStrField): ...


# TODO: this is not workspace id
class VisualseDatasetId(WorkspaceId): ...


class ReleaseUUID(BaseUUIDField): ...


class PermissionSetUUID(BaseUUIDField): ...


@dataclass
class SKU:
    unique_sku_id: str
    base_sku_id: str
    portal_platform_region: str
    gso: int
    name: str
    sku_type: str
    studio_id: int
    product_id: str
    product_name: str
    release_date: str


@dataclass
class Portal:
    portal_platform_region: str
    portal: str
    platform: str
    region: str
    store: str
    abbreviated_name: str


@dataclass
class Studio:
    company_name: str
    studio_id: str


@dataclass
class Region:
    region: str
    country_name: str


class Slicer(Enum):
    STUDIO = "studio"
    SELECTED_MEASURE_ALL = "selected_measure_all"
    SELECTED_MEASURE_REVENUE = "selected_measure_revenue"
    SELECTED_MEASURE_UNITS = "selected_measure_units"
    SELECTED_MEASURE_UNITS_EXCL_RETURNS = "selected_measure_units_excl_returns"
    SELECTED_MEASURE_WISHLIST = "selected_measure_wishlist"
    SELECTED_MEASURE_VISIBILITY = "selected_measure_visibility"
    SALE_DAY_TYPE = "sale_day_type"
    REGION = "region"


class DaxQuery(BaseModel):
    base64_query: str | None = Field(
        default=None,
        title="Base64 DAX query",
    )
    raw_query: str | None = Field(
        default=None,
        title="Field for dev testing that doesn't require base64 encoding",
    )

    @model_validator(mode="before")
    def check_sum(cls, values):
        if all(item is None for item in values.values()):
            raise ValueError("One of 'base64_query' or 'raw_query' needs to be set")
        return values

    @property
    def query(self) -> str:
        return cast(
            str,
            (
                base64.b64decode(self.base64_query).decode()
                if self.base64_query is not None
                else self.raw_query
            ),
        )
