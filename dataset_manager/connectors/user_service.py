from functools import lru_cache

import httpx
from fastapi.concurrency import run_in_threadpool
from pydantic import BaseModel, TypeAdapter

from dataset_manager import config


class UserResponse(BaseModel):
    email: str
    id: str
    legacy_id: int


class UserNotFound(Exception): ...


class UserServiceNotAvailable(Exception): ...


_client_sync = httpx.Client(
    base_url=config.USER_SERVICE_URL,
    headers={"x-api-key": config.USER_SERVICE_KEY},
)


def get_user_by_id_sync(user_id: str) -> UserResponse:
    try:
        response = _client_sync.get(f"/user/{user_id}")
        response.raise_for_status()
        return TypeAdapter(UserResponse).validate_json(response.content)
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            raise UserNotFound()
        if e.response.status_code >= 500:
            raise UserServiceNotAvailable()
        raise e
    except httpx.RequestError:
        raise UserServiceNotAvailable()


async def get_legacy_studio_id_from_user_id(org_id: str):
    return await run_in_threadpool(get_legacy_studio_id_from_user_id_sync, org_id)


@lru_cache(maxsize=1024)
def get_legacy_studio_id_from_user_id_sync(org_id: str):
    return (get_user_by_id_sync(org_id)).legacy_id
