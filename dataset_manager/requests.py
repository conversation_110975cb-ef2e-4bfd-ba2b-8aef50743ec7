import logging
from contextlib import contextmanager, suppress

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

log = logging.getLogger(__name__)


def send_request_with_retry(method: str, url: str, **kwargs) -> requests.Response:
    with _retry_session() as session:
        response = session.request(method.lower(), url=url, **kwargs)

        response_message = None
        with suppress(Exception):  # for extra logging in case of failures
            response_message = response.text  # noqa

        try:
            response.raise_for_status()
        except Exception as e:
            log.error("Got: %s", response_message)
            raise e

        return response


@contextmanager
def _retry_session(
    retries: int = 3,
    backoff_factor: int = 1,
    force_on_statuses: list[int] | None = None,
):
    if force_on_statuses is None:
        force_on_statuses = [429, 500, 502, 503, 504]
    retry_strategy = Retry(
        total=retries,
        status_forcelist=force_on_statuses,
        raise_on_status=False,
        backoff_factor=backoff_factor,
        allowed_methods={"GET", "PUT", "POST", "PATCH"},
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session = requests.Session()
    session.mount("https://", adapter)
    session.mount("http://", adapter)
    try:
        yield session
    finally:
        session.close()
