import collections
import logging
import logging.config
import os
import sys
import threading
from contextlib import contextmanager
from copy import deepcopy

import ecs_logging

log = logging.getLogger(__name__)


_THREAD_LOCAL = threading.local()
_THREAD_LOCAL.pids = collections.defaultdict(dict)


# value used as a prefix for values injected to logging context
RELEASE_CONTEXT_NAME = "dataset_release"


@contextmanager
def add_logging_context(extra_context_name: str, **kwargs):
    """Add extra context to the log.

    Args:
        extra_context_name: name of the context used for logs, each extra will be logged
            under object `extra_context_name`
    """
    new_extra = {
        f"{extra_context_name}.{name}": value for name, value in kwargs.items()
    }
    old_extra = _get_extra()
    _set_extra({**old_extra, **new_extra})
    yield
    _set_extra(old_extra)


def _set_extra(extra):
    _init_extra()
    pid = os.getpid()
    _THREAD_LOCAL.pids[pid] = extra
    return _THREAD_LOCAL.pids[pid]


def _except_logging(exc_type, exc_value, exc_traceback):
    log.exception(
        f"Exception hook. Type: {exc_type}, stacktrace: {exc_traceback}",
        exc_info=exc_value,
    )


def _unraisable_logging(
    exc_type, exc_value=None, exc_traceback=None, err_msg=None, object=None
):
    log.exception(
        f"Unraisable hook. Type: {exc_type}, err_msg: {err_msg}, object: {object}, stacktrace: {exc_traceback}",
        exc_info=exc_value,
    )


def _threading_except_logging(
    exc_type, exc_value=None, exc_traceback=None, thread=None
):
    log.exception(
        f"Threading exception hook. Type: {exc_type}, thread: {thread}, stacktrace: {exc_traceback}",
        exc_info=exc_value,
    )


def _init_extra():
    if not hasattr(_THREAD_LOCAL, "pids"):
        _THREAD_LOCAL.pids = collections.defaultdict(dict)


def _get_extra():
    _init_extra()
    pid = os.getpid()
    return _THREAD_LOCAL.pids[pid]


def get_logging_context():
    return deepcopy(_get_extra())


class ContextFilter(logging.Filter):
    def filter(self, record):
        context = _get_extra()
        for key, value in context.items():
            setattr(record, key, value)
        return True


def configure_logger():
    logging_config = {
        "version": 1,
        "formatters": {"ecs": {"()": ecs_logging.StdlibFormatter}},
        "filters": {
            "context": {
                "()": "dataset_manager.logs.ContextFilter",
            }
        },
        "handlers": {
            "standard_output": {
                "class": "logging.StreamHandler",
                "formatter": "ecs",
                "stream": "ext://sys.stdout",
                "filters": ["context"],
            },
        },
        "loggers": {
            "root": {"level": "INFO", "handlers": ["standard_output"]},
            "dataset_manager": {},
            "uvicorn": {"propagate": True},
        },
    }

    logging.config.dictConfig(logging_config)

    sys.excepthook = _except_logging
    sys.unraisablehook = _unraisable_logging
    threading.excepthook = _threading_except_logging
