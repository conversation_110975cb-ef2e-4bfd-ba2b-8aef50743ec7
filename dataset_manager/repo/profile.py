from datetime import datetime

from pydantic import BaseModel, ConfigDict
from sqlalchemy import Column
from sqlalchemy.dialects.mssql import UNIQUEIDENTIFIER
from sqlalchemy.orm import Session
from sqlalchemy.sql.sqltypes import DateTime, Integer, String

from dataset_manager.connectors.db_engine import Base, filter_query
from dataset_manager.entities import PermissionSetUUID, VersionId


class ProfileProperties(BaseModel):
    studio_id: int
    profile_id: str
    profile_name: str
    active_version_id: VersionId
    permission_set_uuid: PermissionSetUUID


class Profile(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    studio_id: int
    profile_id: str
    profile_name: str
    creation_timestamp: datetime
    active_version_id: VersionId
    permission_set_uuid: PermissionSetUUID | None


class _DBProfile(Base):
    __tablename__ = "profile"
    __table_args__ = {"schema": "WebApp"}
    profile_id = Column(String, primary_key=True)
    profile_name = Column("profile_name", String, nullable=False)
    studio_id = Column("studio_id", Integer, nullable=False, unique=True, index=True)
    creation_timestamp = Column(
        "creation_timestamp", DateTime, nullable=False, default=datetime.utcnow
    )
    active_version_id = Column("active_version_id", String, nullable=False)
    _permission_set_uuid = Column(
        "permission_set_uuid", UNIQUEIDENTIFIER, nullable=True
    )

    @property
    def permission_set_uuid(self):
        return (
            self._permission_set_uuid.lower()
            if self._permission_set_uuid is not None
            else None
        )

    @permission_set_uuid.setter
    def permission_set_uuid(self, permission_set_uuid):
        self._permission_set_uuid = (
            str(permission_set_uuid) if permission_set_uuid is not None else None
        )


class ProfileNotFound(Exception):
    pass


class ProfileRepo:
    def __init__(self, session: Session) -> None:
        self._session = session

    def create(self, new_profile: ProfileProperties) -> Profile:
        orm_profile = _DBProfile(**new_profile.model_dump())
        self._session.add(orm_profile)
        self._session.flush()
        self._session.refresh(orm_profile)
        return Profile.model_validate(orm_profile)

    def get_for_studio(self, studio_id: int) -> Profile:
        orm_profile = filter_query(
            self._session, _DBProfile, studio_id=studio_id
        ).one_or_none()
        if orm_profile is None:
            raise ProfileNotFound
        return Profile.model_validate(orm_profile)

    def exists_for_studio(self, studio_id: int) -> bool:
        return (
            filter_query(self._session, _DBProfile, studio_id=studio_id).one_or_none()
            is not None
        )

    def get_for_active_version(self, active_version_id: VersionId) -> list[Profile]:
        orm_profiles = filter_query(
            self._session, _DBProfile, active_version_id=active_version_id
        ).all()
        return [Profile.model_validate(orm_profile) for orm_profile in orm_profiles]

    def get_all(self) -> list[Profile]:
        orm_profiles = filter_query(self._session, _DBProfile).all()
        if orm_profiles is None:
            raise ProfileNotFound
        return [Profile.model_validate(orm_profile) for orm_profile in orm_profiles]

    def save(self, profile: Profile) -> Profile:
        db_version = self._session.get(
            _DBProfile, ident=profile.profile_id
        ) or _DBProfile(profile_id=profile.profile_id)

        db_version.profile_name = profile.profile_name
        db_version.active_version_id = profile.active_version_id
        db_version.permission_set_uuid = profile.permission_set_uuid

        self._session.add(db_version)
        self._session.flush()

        return self.get_for_studio(studio_id=profile.studio_id)

    def get_for_profile_id(self, profile_id: str) -> Profile:
        orm_profile = filter_query(
            self._session, _DBProfile, profile_id=profile_id
        ).one_or_none()
        if orm_profile is None:
            raise ProfileNotFound
        return Profile.model_validate(orm_profile)

    def get_all_for_permission_set_uuid(
        self, permission_set_uuid: PermissionSetUUID
    ) -> list[Profile]:
        orm_profiles = filter_query(
            self._session, _DBProfile, _permission_set_uuid=permission_set_uuid
        ).all()
        return [Profile.model_validate(orm_profile) for orm_profile in orm_profiles]
