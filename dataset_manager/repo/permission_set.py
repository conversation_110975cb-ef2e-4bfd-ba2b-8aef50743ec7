import json
from hashlib import md5

from pydantic import BaseModel, ConfigDict, field_serializer, field_validator
from sqlalchemy import <PERSON>SO<PERSON>, Column, String, func
from sqlalchemy.dialects.mssql import UNIQUEIDENTIFIER
from sqlalchemy.orm import Session

from dataset_manager.connectors.db_engine import Base, filter_query
from dataset_manager.entities import PermissionSetUUID


class _DBPermissionSet(Base):
    __tablename__ = "permission_set"
    __table_args__ = {"schema": "WebApp"}
    _uuid = Column(
        "uuid",
        UNIQUEIDENTIFIER,
        nullable=False,
        unique=True,
        primary_key=True,
        server_default=func.NEWID(),
    )
    permission_set = Column("permission_set", JSON, nullable=False)
    permission_set_hash = Column(
        "permission_set_hash", String, nullable=False, unique=True
    )

    @property
    def uuid(self):
        return self._uuid.lower()

    @uuid.setter
    def uuid(self, uuid):
        self._uuid = uuid


class Permission(BaseModel):
    studio_id: int
    product_name: str | None


class CreatePermissionSet(BaseModel):
    permission_set: list[Permission]

    @field_validator("permission_set")
    def permission_set_are_sorted(cls, permission_set: list[Permission]):
        return sorted(
            permission_set,
            key=lambda permission: (
                permission.studio_id,
                permission.product_name,
            ),
        )

    @property
    def permission_set_hash(self):
        return md5(json.dumps(self.model_dump()["permission_set"]).encode()).hexdigest()


class PermissionSet(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    uuid: PermissionSetUUID
    permission_set: list[Permission]

    @property
    def permission_set_json(self):
        return json.dumps(self.model_dump()["permission_set"])

    @field_serializer("uuid", when_used="json")
    def serialize_courses_in_order(self, uuid: PermissionSetUUID) -> str:
        return str(uuid).lower()


class PermissionSetNotFound(Exception):
    pass


class PermissionSetRepo:
    def __init__(self, session: Session) -> None:
        self._session = session

    def get(self, uuid: PermissionSetUUID) -> PermissionSet:
        orm_version = filter_query(
            self._session, _DBPermissionSet, _uuid=uuid
        ).one_or_none()
        if orm_version is None:
            raise PermissionSetNotFound
        return PermissionSet.model_validate(orm_version)

    def create(self, new_permission_set: CreatePermissionSet) -> PermissionSet:
        existing_permission_set = (
            self._session.query(_DBPermissionSet)
            .filter(
                _DBPermissionSet.permission_set_hash
                == new_permission_set.permission_set_hash
            )
            .one_or_none()
        )
        if existing_permission_set is not None:
            return self.get(uuid=existing_permission_set.uuid)

        db_premission = _DBPermissionSet(
            permission_set=new_permission_set.model_dump()["permission_set"],
            permission_set_hash=new_permission_set.permission_set_hash,
        )
        self._session.add(db_premission)
        self._session.flush()
        self._session.refresh(db_premission)
        return self.get(uuid=db_premission.uuid)
