from datetime import datetime

from pydantic import BaseModel, ConfigDict
from sqlalchemy import Column
from sqlalchemy.dialects.mssql import UNIQUEIDENTIFIER
from sqlalchemy.orm import Session
from sqlalchemy.sql.sqltypes import DateTime, String

from dataset_manager.connectors.db_engine import Base, filter_query
from dataset_manager.entities import (
    DatasetId,
    PermissionSetUUID,
    ShardWorkspaceId,
    VersionId,
)
from dataset_manager.repo.permission_set import PermissionSet
from dataset_manager.repo.profile import _DBProfile
from dataset_manager.repo.version import ShardVersion, Version, _DBVersion


class Shard(BaseModel):
    model_config = ConfigDict(from_attributes=True, frozen=True)

    version: ShardVersion
    dataset_id: DatasetId
    dataset_name: str | None
    workspace_id: ShardWorkspaceId
    workspace_name: str
    capacity_id: str
    creation_timestamp: datetime
    permission_set_uuid: PermissionSetUUID
    last_refresh_timestamp: datetime | None


class _DBShard(Base):
    __tablename__ = "shard"
    __table_args__ = {"schema": "WebApp"}
    version = Column("version", String(50), nullable=False, index=True)
    dataset_id = Column("dataset_id", String, nullable=False)
    dataset_name = Column("dataset_name", String, nullable=True)
    workspace_id = Column("workspace_id", String, primary_key=True)
    workspace_name = Column("workspace_name", String, nullable=False)
    capacity_id = Column("capacity_id", String, nullable=False)
    creation_timestamp = Column(
        "creation_timestamp", DateTime, nullable=False, default=datetime.utcnow
    )
    _permission_set_uuid = Column(
        "permission_set_uuid",
        UNIQUEIDENTIFIER,
        nullable=False,
        index=True,
    )
    last_refresh_timestamp = Column("last_refresh_timestamp", DateTime, nullable=True)

    @property
    def permission_set_uuid(self):
        return str(self._permission_set_uuid).lower()

    @permission_set_uuid.setter
    def permission_set_uuid(self, permission_set_uuid):
        self._permission_set_uuid = permission_set_uuid


class ShardNotFound(Exception):
    pass


class ShardRepo:
    def __init__(self, session: Session) -> None:
        self._session = session

    def all(self) -> list[Shard]:
        orm_shards = filter_query(self._session, _DBShard).all()
        return [Shard.model_validate(orm_shard) for orm_shard in orm_shards]

    def get_by_workspace_id(self, workspace_id: ShardWorkspaceId) -> Shard:
        orm_shard = filter_query(
            self._session, _DBShard, workspace_id=workspace_id
        ).one_or_none()
        if orm_shard is None:
            raise ShardNotFound
        return Shard.model_validate(orm_shard)

    def get_for_permission_set_and_version(
        self, permission_set: PermissionSet, version: Version
    ) -> Shard:
        orm_shard = filter_query(
            self._session,
            _DBShard,
            _permission_set_uuid=permission_set.uuid,
            version=version.shard_version,
        ).one_or_none()
        if orm_shard is None:
            raise ShardNotFound
        return Shard.model_validate(orm_shard)

    def get_for_studio_id_and_version(
        self, studio_id: int, version_id: VersionId | None = None
    ) -> Shard:
        orm_shard = self._search_query(
            studio_ids=[studio_id], version_id=version_id
        ).one_or_none()
        if orm_shard is None:
            raise ShardNotFound
        return Shard.model_validate(orm_shard)

    def search(
        self, studio_ids: list[int] | None, version_id: VersionId | None = None
    ) -> list[Shard]:
        query = self._search_query(studio_ids=studio_ids, version_id=version_id)
        return [Shard.model_validate(orm_shard) for orm_shard in query.all()]

    def _search_query(
        self, studio_ids: list[int] | None, version_id: VersionId | None = None
    ):
        query = (
            self._session.query(_DBShard)
            .join(_DBVersion, _DBVersion.shard_version == _DBShard.version)
            .join(
                _DBProfile,
                _DBProfile._permission_set_uuid == _DBShard._permission_set_uuid,
            )
        )
        if studio_ids is not None and len(studio_ids) > 0:
            query = query.filter(_DBProfile.studio_id.in_(studio_ids))
        if version_id is not None:
            query = query.filter(_DBVersion.id == version_id)
        else:
            query = query.filter(_DBVersion.id == _DBProfile.active_version_id)
        return query

    def save(self, shard: Shard) -> Shard:
        db_shard = self._session.get(_DBShard, ident=shard.workspace_id) or _DBShard(
            workspace_id=shard.workspace_id
        )

        db_shard.version = shard.version
        db_shard.dataset_id = shard.dataset_id
        db_shard.dataset_name = None
        db_shard.workspace_id = shard.workspace_id
        db_shard.workspace_name = shard.workspace_name
        db_shard.capacity_id = shard.capacity_id
        db_shard.creation_timestamp = shard.creation_timestamp
        db_shard.permission_set_uuid = shard.permission_set_uuid
        db_shard.last_refresh_timestamp = shard.last_refresh_timestamp

        self._session.add(db_shard)
        self._session.flush()

        return self.get_by_workspace_id(workspace_id=shard.workspace_id)

    def delete(self, shard: Shard):
        orm_capacity = filter_query(
            self._session, _DBShard, workspace_id=shard.workspace_id
        ).one_or_none()
        if orm_capacity is None:
            raise ShardNotFound
        self._session.delete(orm_capacity)
