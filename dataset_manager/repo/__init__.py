from dataclasses import dataclass

from sqlalchemy.orm import Session

from dataset_manager.repo.capacity import CapacityRepo
from dataset_manager.repo.permission_set import PermissionSetRepo
from dataset_manager.repo.profile import ProfileRepo
from dataset_manager.repo.release import ReleaseRepo
from dataset_manager.repo.shard import ShardRepo
from dataset_manager.repo.version import VersionRepo


@dataclass
class Repo:
    capacity: CapacityRepo
    permission_set: PermissionSetRepo
    profile: ProfileRepo
    release: ReleaseRepo
    shard: ShardRepo
    version: VersionRepo


def get_repo(session: Session) -> Repo:
    return Repo(
        capacity=CapacityRepo(session=session),
        permission_set=PermissionSetRepo(session=session),
        profile=ProfileRepo(session=session),
        release=ReleaseRepo(session=session),
        shard=ShardRepo(session=session),
        version=VersionRepo(session=session),
    )
