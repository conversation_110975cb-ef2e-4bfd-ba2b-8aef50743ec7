import logging
from concurrent.futures import FIRST_COMPLETED, ThreadPoolExecutor, wait
from time import sleep

log = logging.getLogger(__name__)


def infinite_executor(
    wroker_fn,
    get_next_jobs_fn,
    max_paraller_jobs=10,
    thread_name_prefix="",
    idle_sleep_seconds=10,
):
    with ThreadPoolExecutor(
        max_workers=max_paraller_jobs, thread_name_prefix=thread_name_prefix
    ) as executor:
        jobs_in_progress = {}

        def _remove_job_in_progress(future):
            del jobs_in_progress[future]

        while True:
            jobs_in_progress_count = len(jobs_in_progress)
            if jobs_in_progress_count < max_paraller_jobs:
                next_jobs = get_next_jobs_fn(
                    max_paraller_jobs - jobs_in_progress_count,
                    jobs_in_progress.values(),
                )
                if len(next_jobs) == 0:
                    sleep(idle_sleep_seconds)
                    continue
                elif next_jobs is not None:
                    log.info(
                        "Getting next jobs count %s, ignored: %s",
                        max_paraller_jobs - jobs_in_progress_count,
                        [release.uuid for release in jobs_in_progress.values()],
                    )
                    log.info("Scheduling next jobs %s", next_jobs)
                    for next_job in next_jobs:
                        future = executor.submit(wroker_fn, next_job)
                        jobs_in_progress[future] = next_job
                        future.add_done_callback(_remove_job_in_progress)

            wait(jobs_in_progress.keys(), return_when=FIRST_COMPLETED)
