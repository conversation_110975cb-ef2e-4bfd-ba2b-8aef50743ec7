#!/bin/bash

set -e # exit immediately on failure
set -x # display all commands


if [[ $SHELL == "/bin/zsh" ]]; then 
    export RC_FILE="$HOME/.zshrc"
else 
    export RC_FILE="$HOME/.bashrc" 
fi

cd "$(dirname "$0")"


sudo apt update


# Install Azure CLI
if ! command -v az &> /dev/null; then
    curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
    sudo apt-get install azure-cli=2.35.0-1~focal # Fixed version, we are not compatible with the newest
else
    echo "azure cli already installed"
fi


# Install Azure Functions Core Tools
if ! command -v func &> /dev/null; then
    curl https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > microsoft.gpg
    sudo mv microsoft.gpg /etc/apt/trusted.gpg.d/microsoft.gpg -f
    sudo sh -c 'echo "deb [arch=amd64] https://packages.microsoft.com/repos/microsoft-ubuntu-$(lsb_release -cs)-prod $(lsb_release -cs) main" > /etc/apt/sources.list.d/dotnetdev.list'
    sudo apt update
    sudo apt-get install azure-functions-core-tools-3
else
    echo "azure function core tool already installed"
fi


# Install Python 3.10
sudo apt install software-properties-common -y
sudo add-apt-repository ppa:deadsnakes/ppa -y
sudo apt install python3.10 python3.10-dev python3.10-distutils -y


# Install poetry
if ! command -v poetry &> /dev/null; then
    curl -sSL https://install.python-poetry.org | python3 -
    echo 'export PATH="$HOME/.local/bin:$PATH"' | tee -a ${RC_FILE}
else
    echo "poetry already installed"
fi


# Install MSODBC
sudo su <<HERE
curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add -
curl https://packages.microsoft.com/config/ubuntu/20.04/prod.list > /etc/apt/sources.list.d/mssql-release.list
HERE
sudo apt update
sudo ACCEPT_EULA=Y apt-get install -y unixodbc unixodbc-dev msodbcsql17


# Install g++
sudo apt-get install g++ -y


# Install jq
sudo apt-get install jq -y


# Create Azure resources
if ! az account show &> /dev/null ; then
    echo "login to your azure account"
    # see https://github.com/Azure/azure-cli/issues/14656
    DISPLAY=none az login
    az account set --subscription indiebi-dev-playground
fi


# Create DEV_USERNAME environment variable
export DEV_USERNAME=$(az account show --query user.name -o tsv | cut -d '@' -f 1 | tr . -)
sed -i '/export DEV_USERNAME=/d' $RC_FILE
echo export DEV_USERNAME=$DEV_USERNAME | tee -a ${RC_FILE}


echo "Reloading shell"
source ~/.bashrc


# Set line-endings to unified LF used among the repos
git config --global core.autocrlf false
git config --global core.eol lf


# install poetry packages for dataset-manager repo
poetry env use 3.10
poetry install

echo "Installation complete!"
