# Demo Mode

For now demo mode have separate endpoints that uses static config. All updates to config needs to be done via repository and are set in `DemoModeSettings` class inside `dataset_manager/config.py` file.

## Demo Mode workspace requirements
All reports and models needs to be placed in a single pbi workspace. Both `dataset-manager-dev` and `dataset-manager-prod` apps needs access to this workspaces. If you want to run helper script from local machine, you also need access to the workspace.

![alt text](pbi_access.png)

## Configuration update
There is a helper script `scripts/update_demo_mode_cfg.py` that prints `dataset_id` and all `reports_id` inside given workspace. You need to copy them manually to `config.py` file. 

Those values will be also passed to `static/demo-mode-desktop-definitions.json` automatically. But remember that it always uses newest `static/desktop-definitions-template.json` file. If you want to keep older version, then some manual action will be needed.


### VSCode config
There is additional VSCode run configuration that allows you to pass workspace and run the script.
![alt text](vscode.png)
