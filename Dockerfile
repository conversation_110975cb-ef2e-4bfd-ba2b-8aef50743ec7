FROM --platform=linux/amd64 python:3.13-bullseye as builder
# Force build on AMD platform to replicate the behaviour from Gitlab on Mac M1 machines

# 🚀 Optimized for size and build speed using techniques from 
# https://indiebi.atlassian.net/wiki/spaces/DPT/pages/362512422/Optimizing+Dockerfiles+for+Python

WORKDIR /app

RUN curl -sL https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor | tee /etc/apt/trusted.gpg.d/microsoft.gpg > /dev/null &&  \
    curl https://packages.microsoft.com/config/debian/11/prod.list > /etc/apt/sources.list.d/mssql-release.list

RUN pip install poetry && \
    poetry config virtualenvs.in-project true

COPY poetry.lock pyproject.toml /app/
# removing unneeded directories here is more about speeding up Kaniko build than reducing target image size
RUN poetry install --only main,test --no-interaction --no-root --no-cache && \
    rm -rf /root/.local/ && \
    rm -rf /root/.cache/

FROM --platform=linux/amd64 python:3.13-slim-bullseye


COPY --from=builder /etc/apt/sources.list.d/mssql-release.list /etc/apt/sources.list.d/mssql-release.list
COPY --from=builder /etc/apt/trusted.gpg.d/microsoft.gpg /etc/apt/trusted.gpg.d/microsoft.gpg

RUN apt-get update && \
    ACCEPT_EULA=Y apt-get install --no-install-recommends -y unixodbc msodbcsql17  && \
    rm -rf /var/lib/apt/lists/* && \
    groupadd --gid 1000 service && useradd --uid 1000 --gid 1000 -r -g service service


COPY --from=builder --chown=service:service /app/.venv /app/.venv
COPY --chown=service:service alembic /app/alembic
COPY --chown=service:service alembic.ini /app/alembic.ini
COPY --chown=service:service dataset_manager /app/dataset_manager
COPY --chown=service:service static /app/static
COPY --chown=service:service test /app/test

USER service

ARG DOCKER_TAG
ENV DOCKER_TAG=$DOCKER_TAG
ENV SENTRY_RELEASE=$DOCKER_TAG

ARG DOCKER_BUILD_TIMESTAMP
ENV DOCKER_BUILD_TIMESTAMP=$DOCKER_BUILD_TIMESTAMP
ENV PATH="/app/.venv/bin:${PATH}"

WORKDIR /app


CMD ["uvicorn", "dataset_manager.main:app", "--host", "0.0.0.0", "--port", "8000"]