import pytest
from sqlalchemy.orm import Session
from starlette.status import HTTP_403_FORBIDDEN, HTTP_404_NOT_FOUND

from dataset_manager.repo.release import _DBRelease
from dataset_manager.repo.version import _DBVersion


def test_API_call_without_auth_header_fails(db_session: Session, client):
    response = client.get("/version/default")
    assert response.status_code == HTTP_403_FORBIDDEN


@pytest.fixture
def existing_active_and_inactive_versions(version_factory):
    version_factory()
    version_factory(id="2.0.22", is_default=False)
    version_factory(id="2.0.22_dev", is_default=False)


@pytest.fixture
def existing_profiles(profile_factory):
    profile_factory()
    profile_factory(profile_id="wb9cdg98-o222-izzi-b60h-8g8d4ee8b110", studio_id=2)
    profile_factory(profile_id="wb9cdg98-o333-izzi-b60h-8g8d4ee8b110", studio_id=3)
    profile_factory(profile_id="wb9cdg98-o444-izzi-b60h-8g8d4ee8b110", studio_id=4)
    profile_factory(profile_id="wb9cdg98-o555-izzi-b60h-8g8d4ee8b110", studio_id=123)


def test_not_existing_version_returns_404(
    db_session: Session,
    client,
    authorization_header,
    existing_active_and_inactive_versions,
):
    response = client.get(
        "/version/not_existing_id",
        headers=authorization_header,
    )
    assert response.status_code == HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Version not found"}


def test_API_call_returns_default_version(
    db_session: Session,
    client,
    authorization_header,
    existing_active_and_inactive_versions,
):
    response = client.get(
        "/version/default",
        headers=authorization_header,
    ).json()
    assert response["id"] == "2.0.4"


def test_get_default_when_not_set_returns_404(
    db_session: Session, client, authorization_header
):
    response = client.get(
        "/version/default",
        headers=authorization_header,
    )

    assert response.status_code == HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Version not found"}


def test_API_call_returns_all_versions(
    db_session: Session,
    client,
    authorization_header,
    existing_active_and_inactive_versions,
):
    response = client.get(
        "/version/all",
        headers=authorization_header,
    ).json()

    assert response[0]["id"] == "2.0.22"
    assert response[1]["id"] == "2.0.22_dev"
    assert response[2]["id"] == "2.0.4"


def test_existing_versions_return_correct_ids(
    db_session: Session,
    client,
    authorization_header,
    existing_active_and_inactive_versions,
):
    response = client.get(
        "/version/default",
        headers=authorization_header,
    ).json()
    assert response["id"] == "2.0.4"

    response = client.get(
        "/version/2.0.22",
        headers=authorization_header,
    ).json()
    assert response["id"] == "2.0.22"


def test_release_existing_version_to_specific_studios(
    db_session: Session,
    client,
    authorization_header,
    existing_active_and_inactive_versions,
    existing_profiles,
):
    response = client.put(
        "/version/2.0.22/release_for", headers=authorization_header, json=[1, 2]
    ).json()

    assert response["id"] == "2.0.22"

    all_releases = sorted(db_session.query(_DBRelease).all(), key=lambda r: r.studio_id)

    assert all_releases[0].studio_id == 1
    assert all_releases[0].is_full_recreate is False
    assert all_releases[1].studio_id == 2
    assert all_releases[1].is_full_recreate is False
    assert len(all_releases) == 2


def test_release_existing_version_to_specific_studios_foreces_recreate_for_dev_version(
    db_session: Session,
    client,
    authorization_header,
    existing_active_and_inactive_versions,
    existing_profiles,
):
    response = client.put(
        "/version/2.0.22_dev/release_for", headers=authorization_header, json=[1, 2]
    ).json()

    assert response["id"] == "2.0.22_dev"

    all_releases = sorted(db_session.query(_DBRelease).all(), key=lambda r: r.studio_id)

    assert all_releases[0].studio_id == 1
    assert all_releases[0].is_full_recreate is True
    assert all_releases[1].studio_id == 2
    assert all_releases[1].is_full_recreate is True
    assert len(all_releases) == 2


def test_release_existing_version_to_specific_studios_foreces_recreate_for_force_recreate(
    db_session: Session,
    client,
    authorization_header,
    existing_active_and_inactive_versions,
    existing_profiles,
):
    response = client.put(
        "/version/2.0.22/release_for/force_recreate",
        headers=authorization_header,
        json=[1, 2],
    ).json()

    assert response["id"] == "2.0.22"

    all_releases = sorted(db_session.query(_DBRelease).all(), key=lambda r: r.studio_id)

    assert all_releases[0].studio_id == 1
    assert all_releases[0].is_full_recreate is True
    assert all_releases[1].studio_id == 2
    assert all_releases[1].is_full_recreate is True
    assert len(all_releases) == 2


def test_release_existing_version_to_all_studios(
    db_session: Session,
    client,
    authorization_header,
    existing_active_and_inactive_versions,
    existing_profiles,
):
    assert len(db_session.query(_DBRelease).all()) == 0

    response = client.put(
        "/version/2.0.22/release_for/all", headers=authorization_header
    ).json()
    assert response["id"] == "2.0.22"

    all_releases = sorted(db_session.query(_DBRelease).all(), key=lambda r: r.studio_id)

    assert all_releases[0].studio_id == 1
    assert all_releases[0].is_full_recreate is False
    assert all_releases[1].studio_id == 2
    assert all_releases[1].is_full_recreate is False
    assert all_releases[2].studio_id == 3
    assert all_releases[2].is_full_recreate is False
    assert all_releases[3].studio_id == 4
    assert all_releases[3].is_full_recreate is False
    assert all_releases[4].studio_id == 123
    assert all_releases[4].is_full_recreate is False
    assert len(all_releases) == 5


def test_realease_for_all_uses_bulk_insert(
    db_session: Session,
    client,
    authorization_header,
    existing_active_and_inactive_versions,
    existing_profiles,
):
    from sqlalchemy import event

    class QueryCounter(object):
        """Context manager to count SQLALchemy queries."""

        def __init__(self, connection):
            self.connection = connection.engine
            self.count = 0

        def __enter__(self):
            event.listen(self.connection, "before_cursor_execute", self.callback)
            return self

        def __exit__(self, *args, **kwargs):
            event.remove(self.connection, "before_cursor_execute", self.callback)

        def callback(self, *args, **kwargs):
            self.count += 1

    assert len(db_session.query(_DBRelease).all()) == 0
    with QueryCounter(db_session.connection()) as counter:
        response = client.put(
            "/version/2.0.22/release_for/all", headers=authorization_header
        ).json()
    assert response["id"] == "2.0.22"
    assert counter.count == 4


def test_release_existing_version_to_all_studios_force_recreate(
    db_session: Session,
    client,
    authorization_header,
    existing_active_and_inactive_versions,
    existing_profiles,
):
    assert len(db_session.query(_DBRelease).all()) == 0

    response = client.put(
        "/version/2.0.22/release_for/all/force_recreate", headers=authorization_header
    ).json()
    assert response["id"] == "2.0.22"

    all_releases = sorted(db_session.query(_DBRelease).all(), key=lambda r: r.studio_id)

    assert all_releases[0].studio_id == 1
    assert all_releases[0].is_full_recreate is True
    assert all_releases[1].studio_id == 2
    assert all_releases[1].is_full_recreate is True
    assert all_releases[2].studio_id == 3
    assert all_releases[2].is_full_recreate is True
    assert all_releases[3].studio_id == 4
    assert all_releases[3].is_full_recreate is True
    assert all_releases[4].studio_id == 123
    assert all_releases[4].is_full_recreate is True
    assert len(all_releases) == 5


def test_change_default_version(
    db_session: Session,
    client,
    authorization_header,
    existing_active_and_inactive_versions,
):
    response = client.get(
        "/version/default",
        headers=authorization_header,
    ).json()
    all_default_version = len(
        db_session.query(_DBVersion).filter(_DBVersion.is_default == True).all()
    )

    assert response["id"] == "2.0.4"
    assert all_default_version == 1

    client.put(
        "/version/2.0.22/set_default",
        headers=authorization_header,
    )

    response = client.get(
        "/version/default",
        headers=authorization_header,
    ).json()
    all_default_version = len(
        db_session.query(_DBVersion).filter(_DBVersion.is_default == True).all()
    )
    assert response["id"] == "2.0.22"
    assert all_default_version == 1
