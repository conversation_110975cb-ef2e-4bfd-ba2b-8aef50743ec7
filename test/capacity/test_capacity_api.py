import pytest
from sqlalchemy.orm import Session
from starlette.status import HTTP_200_OK, HTTP_404_NOT_FOUND

from dataset_manager.logic.capacity import CapacityScalingError
from dataset_manager.repo.capacity import CapacityRepo, CapacityState


def test_get_capacities(
    db_session: Session,
    client,
    authorization_header,
    capacity_factory,
):
    capacity_factory()

    response = client.get(
        "/capacity",
        headers=authorization_header,
    )
    data = response.json()

    assert data[0]["id"] == "xxx-xxx-capacity"
    assert data[0]["name"] == "activecapacity"
    assert data[0]["tier"] == "A2"
    assert data[0]["state"] == CapacityState.ACTIVE
    assert data[0]["is_default"] is False


def test_create_capacity(
    db_session: Session,
    client,
    authorization_header,
    get_details_of_capacity,
    power_bi_api,
):
    response = client.post(
        "/capacity",
        headers=authorization_header,
        json={"capacity_name": "activecapacity"},
    )

    capacity = response.json()
    assert capacity["id"] == "xxx-xxx-capacity"
    assert capacity["name"] == "activecapacity"
    assert capacity["tier"] == "A2"
    assert capacity["state"] == CapacityState.ACTIVE
    assert capacity["is_default"] is False


def test_scale_up_capacity_if_possible(
    db_session: Session,
    client,
    authorization_header,
    capacity_factory,
    get_management_details_of_capacity,
    scale_up_capacity,
):
    get_management_details_of_capacity(capacity_tier="A2")
    capacity_factory()

    response = client.put(
        "/capacity/activecapacity/scale_up",
        headers=authorization_header,
    )

    capacity = response.json()
    assert capacity["tier"] == "A3"


def test_scale_down_capacity_if_possible(
    db_session: Session,
    client,
    authorization_header,
    capacity_factory,
    get_management_details_of_capacity,
    scale_down_capacity,
):
    get_management_details_of_capacity(capacity_tier="A2")
    capacity_factory()

    response = client.put(
        "/capacity/activecapacity/scale_down",
        headers=authorization_header,
    )

    capacity = response.json()
    assert capacity["tier"] == "A1"


def test_scale_up_A6_capacity_scales_up_to_A8_and_scales_down_to_A2(
    db_session: Session,
    client,
    authorization_header,
    capacity_factory,
    get_capacity_details_to_update_db,
    scale_up_to_a8_patch,
    get_capacity_details_after_scaling_to_a8_backoff,
    get_capacity_details_after_scaling_to_a8_succeeded,
    scale_to_a2_patch,
    get_capacity_details_after_reset_to_a2_succeeded,
):
    capacity_factory(tier="A6")

    response = client.put(
        "/capacity/activecapacity/scale_up",
        headers=authorization_header,
    )

    capacity = response.json()
    assert capacity["tier"] == "A2"


def test_scale_up_A6_capacity_scale_up_to_A8_fail_and_remains_at_A6(
    db_session: Session,
    client,
    authorization_header,
    capacity_factory,
    get_capacity_details_to_update_db,
    scale_up_to_a8_patch,
    get_capacity_details_after_scaling_to_a8_backoff,
    get_capacity_details_after_scaling_to_a8_failed,
):
    capacity_factory(tier="A6")

    with pytest.raises(CapacityScalingError):
        client.put(
            "/capacity/activecapacity/scale_up",
            headers=authorization_header,
        )
    assert (
        CapacityRepo(db_session).get_by_name("activecapacity").state
        == CapacityState.UNKNOWN
    )
    assert CapacityRepo(db_session).get_by_name("activecapacity").tier == "A6"


def test_scale_up_A6_capacity_to_A8_and_fails_to_scale_down_remains_A8_to_be_picked_up_by_autoscaler(
    db_session: Session,
    client,
    authorization_header,
    capacity_factory,
    get_capacity_details_to_update_db,
    scale_up_to_a8_patch,
    get_capacity_details_after_scaling_to_a8_succeeded,
    scale_to_a2_patch,
    get_capacity_details_after_reset_to_a2_failed,
):
    capacity_factory(tier="A6")

    with pytest.raises(CapacityScalingError):
        client.put(
            "/capacity/activecapacity/scale_up",
            headers=authorization_header,
        )
    assert (
        CapacityRepo(db_session).get_by_name("activecapacity").state
        == CapacityState.UNKNOWN
    )
    assert CapacityRepo(db_session).get_by_name("activecapacity").tier == "A8"


def test_scale_down_capacity_if_not_possible_does_nothing(
    db_session: Session,
    client,
    authorization_header,
    capacity_factory,
    get_management_details_of_capacity,
):
    get_management_details_of_capacity(capacity_tier="A1")
    capacity_factory(tier="A1")

    response = client.put(
        "/capacity/activecapacity/scale_down",
        headers=authorization_header,
    )

    capacity = response.json()
    assert capacity["tier"] == "A1"


def test_suspend_active_capacity(
    db_session: Session,
    client,
    authorization_header,
    pause_capacity,
    get_details_of_capacity_to_be_paused,
    capacity_factory,
    power_bi_api,
    get_management_details_of_capacity,
):
    get_management_details_of_capacity(state="Succeeded")
    capacity_factory()

    response = client.put(
        "/capacity/activecapacity/pause",
        headers=authorization_header,
    )

    capacity = response.json()
    assert capacity["state"] == CapacityState.PAUSED


def test_pause_paused_capacity_does_nothing(
    db_session: Session,
    client,
    authorization_header,
    capacity_factory,
    get_management_details_of_capacity,
):
    get_management_details_of_capacity(state="Paused")
    capacity_factory(state=CapacityState.PAUSED)

    response = client.put(
        "/capacity/activecapacity/pause",
        headers=authorization_header,
    )

    capacity = response.json()
    assert capacity["state"] == CapacityState.PAUSED


def test_resume_paused_capacity(
    db_session: Session,
    client,
    authorization_header,
    resume_capacity,
    get_management_details_of_capacity,
    get_details_capacity_to_be_resumed,
    capacity_factory,
    power_bi_api,
):
    get_management_details_of_capacity(state="Paused")
    capacity_factory(state=CapacityState.PAUSED)

    response = client.put(
        "/capacity/activecapacity/resume",
        headers=authorization_header,
    )

    capacity = response.json()
    assert capacity["state"] == CapacityState.ACTIVE


def test_resume_active_capacity_does_nothing(
    db_session: Session,
    client,
    authorization_header,
    capacity_factory,
    get_management_details_of_capacity,
):
    get_management_details_of_capacity(state="Succeeded")
    capacity_factory()

    response = client.put(
        "/capacity/activecapacity/resume",
        headers=authorization_header,
    )

    capacity = response.json()
    assert capacity["state"] == CapacityState.ACTIVE


def test_set_default_capacity(
    db_session: Session,
    client,
    authorization_header,
    capacity_factory,
):
    capacity_factory()
    capacity_factory(id="yyy-yyy-yyy", name="capacity2")
    capacity_factory(id="aaa-aaa-aaa", name="capacity3", is_default=True)

    response = client.put(
        "/capacity/activecapacity/set_default",
        headers=authorization_header,
    )

    default_capacity = response.json()
    assert default_capacity["name"] == "activecapacity"
    assert default_capacity["is_default"] is True

    response = client.get(
        "/capacity",
        headers=authorization_header,
    )

    data = response.json()
    assert sum([capacity["is_default"] for capacity in data]) == 1


def test_set_default_capacity_for_releases(
    db_session: Session,
    client,
    authorization_header,
    capacity_factory,
):
    capacity_factory(name="capacity_for_releases")
    capacity_factory(id="yyy-yyy-yyy", name="capacity2")
    capacity_factory(id="xxx-xxx-xxx", name="capacity3", is_default_for_releases=True)

    response = client.put(
        "/capacity/capacity_for_releases/set_default_for_releases",
        headers=authorization_header,
    )

    default_capacity = response.json()
    assert default_capacity["name"] == "capacity_for_releases"
    assert default_capacity["is_default_for_releases"] is True

    response = client.get(
        "/capacity",
        headers=authorization_header,
    )

    data = response.json()
    assert sum([capacity["is_default_for_releases"] for capacity in data]) == 1


def test_delete_capacity_paused_capacity(
    db_session: Session,
    client,
    authorization_header,
    capacity_factory,
    get_management_details_of_capacity,
):
    get_management_details_of_capacity(state="Paused")
    capacity_factory(state=CapacityState.PAUSED)
    response = client.delete(
        "/capacity/activecapacity",
        headers=authorization_header,
    )
    assert response.status_code == HTTP_200_OK


def test_delete_capacity_active_capacity(
    db_session: Session,
    client,
    authorization_header,
    pause_capacity,
    get_management_details_of_capacity,
    get_details_of_capacity_to_be_paused,
    capacity_factory,
    power_bi_api,
):
    get_management_details_of_capacity(state="Succeeded")
    capacity_factory()

    response = client.delete(
        "/capacity/activecapacity",
        headers=authorization_header,
    )

    assert response.status_code == HTTP_200_OK


def test_delete_non_existing_capacity(
    db_session: Session, client, authorization_header
):
    response = client.delete(
        "/capacity/activecapacity",
        headers=authorization_header,
    )
    assert response.status_code == HTTP_404_NOT_FOUND
