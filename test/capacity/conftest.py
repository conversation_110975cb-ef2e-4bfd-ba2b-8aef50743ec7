import pytest
import responses
from responses import matchers


@pytest.fixture
def get_details_of_capacity(
    responses_mock: responses.RequestsMock, get_management_details_of_capacity
):
    get_management_details_of_capacity()
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/capacities",
        json={
            "value": [
                {
                    "id": "xxx-xxx-capacity",
                    "displayName": "activecapacity",
                    "admins": ["<EMAIL>"],
                    "sku": "A2",
                    "state": "Active",
                    "capacityUserAccessRight": "Admin",
                    "region": "West Europe",
                    "users": [],
                }
            ],
        },
    )


@pytest.fixture
def scale_up_capacity(
    responses_mock: responses.RequestsMock,
):
    responses_mock.patch(
        "https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity?api-version=2021-01-01",
        match=[
            matchers.json_params_matcher({"sku": {"name": "A3", "tier": "PBIE_Azure"}})
        ],
        json={
            "properties": {
                "provisioningState": "Succeeded",
                "state": "Succeeded",
                "administration": {"members": ["<EMAIL>"]},
            },
            "id": "/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity",
            "name": "activecapacity",
            "type": "Microsoft.PowerBIDedicated/capacities",
            "location": "West Europe",
            "sku": {"name": "A3", "tier": "PBIE_Azure", "capacity": 1},
            "tags": {"env": "dev", "purpose": "releases", "team": "data"},
        },
    )


@pytest.fixture
def get_capacity_details_to_update_db(
    responses_mock: responses.RequestsMock,
):
    url = "https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity?api-version=2021-01-01"
    responses_mock.get(
        url=url,
        json={
            "properties": {
                "mode": "Gen2",
                "provisioningState": "Succeeded",
                "state": "Succeeded",
                "administration": {"members": ["<EMAIL>"]},
            },
            "id": "/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity",
            "name": "activecapacity",
            "type": "Microsoft.PowerBIDedicated/capacities",
            "location": "West Europe",
            "sku": {"name": "A6", "tier": "PBIE_Azure", "capacity": 1},
            "tags": {"env": "dev", "purpose": "releases", "team": "data"},
        },
    )


@pytest.fixture
def scale_up_to_a8_patch(
    responses_mock: responses.RequestsMock,
):
    url = "https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity?api-version=2021-01-01"

    responses_mock.patch(
        url=url,
        match=[
            matchers.json_params_matcher({"sku": {"name": "A8", "tier": "PBIE_Azure"}})
        ],
    )


@pytest.fixture
def get_capacity_details_after_scaling_to_a8_backoff(
    responses_mock: responses.RequestsMock,
):
    url = "https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity?api-version=2021-01-01"
    responses_mock.get(
        url=url,
        json={
            "properties": {
                "mode": "Gen2",
                "provisioningState": "Succeeded",
                "state": "Updating",
                "administration": {"members": ["<EMAIL>"]},
            },
            "id": "/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity",
            "name": "activecapacity",
            "type": "Microsoft.PowerBIDedicated/capacities",
            "location": "West Europe",
            "sku": {"name": "A8", "tier": "PBIE_Azure", "capacity": 1},
            "tags": {"env": "dev", "purpose": "releases", "team": "data"},
        },
    )


@pytest.fixture
def get_capacity_details_after_scaling_to_a8_failed(
    responses_mock: responses.RequestsMock,
):
    url = "https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity?api-version=2021-01-01"
    responses_mock.get(
        url=url,
        json={
            "properties": {
                "mode": "Gen2",
                "provisioningState": "Succeeded",
                "state": "Failed",
                "administration": {"members": ["<EMAIL>"]},
            },
            "id": "/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity",
            "name": "activecapacity",
            "type": "Microsoft.PowerBIDedicated/capacities",
            "location": "West Europe",
            "sku": {"name": "A8", "tier": "PBIE_Azure", "capacity": 1},
            "tags": {"env": "dev", "purpose": "releases", "team": "data"},
        },
    )


@pytest.fixture
def get_capacity_details_after_scaling_to_a8_succeeded(
    responses_mock: responses.RequestsMock,
):
    url = "https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity?api-version=2021-01-01"
    responses_mock.get(
        url=url,
        json={
            "properties": {
                "mode": "Gen2",
                "provisioningState": "Succeeded",
                "state": "Succeeded",
                "administration": {"members": ["<EMAIL>"]},
            },
            "id": "/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity",
            "name": "activecapacity",
            "type": "Microsoft.PowerBIDedicated/capacities",
            "location": "West Europe",
            "sku": {"name": "A8", "tier": "PBIE_Azure", "capacity": 1},
            "tags": {"env": "dev", "purpose": "releases", "team": "data"},
        },
    )


@pytest.fixture
def scale_to_a2_patch(
    responses_mock: responses.RequestsMock,
):
    url = "https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity?api-version=2021-01-01"

    responses_mock.patch(
        url=url,
        match=[
            matchers.json_params_matcher({"sku": {"name": "A2", "tier": "PBIE_Azure"}})
        ],
    )


@pytest.fixture
def get_capacity_details_after_reset_to_a2_succeeded(
    responses_mock: responses.RequestsMock,
):
    url = "https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity?api-version=2021-01-01"
    responses_mock.get(
        url=url,
        json={
            "properties": {
                "mode": "Gen2",
                "provisioningState": "Succeeded",
                "state": "Succeeded",
                "administration": {"members": ["<EMAIL>"]},
            },
            "id": "/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity",
            "name": "activecapacity",
            "type": "Microsoft.PowerBIDedicated/capacities",
            "location": "West Europe",
            "sku": {"name": "A2", "tier": "PBIE_Azure", "capacity": 1},
            "tags": {"env": "dev", "purpose": "releases", "team": "data"},
        },
    )


@pytest.fixture
def get_capacity_details_after_reset_to_a2_failed(
    responses_mock: responses.RequestsMock,
):
    url = "https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity?api-version=2021-01-01"
    responses_mock.get(
        url=url,
        json={
            "properties": {
                "mode": "Gen2",
                "provisioningState": "Succeeded",
                "state": "Failed",
                "administration": {"members": ["<EMAIL>"]},
            },
            "id": "/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity",
            "name": "activecapacity",
            "type": "Microsoft.PowerBIDedicated/capacities",
            "location": "West Europe",
            "sku": {"name": "A2", "tier": "PBIE_Azure", "capacity": 1},
            "tags": {"env": "dev", "purpose": "releases", "team": "data"},
        },
    )


@pytest.fixture
def scale_down_capacity(
    responses_mock: responses.RequestsMock,
):
    responses_mock.patch(
        "https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity?api-version=2021-01-01",
        match=[
            matchers.json_params_matcher({"sku": {"name": "A1", "tier": "PBIE_Azure"}})
        ],
        json={
            "properties": {
                "provisioningState": "Succeeded",
                "state": "Succeeded",
                "administration": {"members": ["<EMAIL>"]},
            },
            "id": "/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity",
            "name": "activecapacity",
            "type": "Microsoft.PowerBIDedicated/capacities",
            "location": "West Europe",
            "sku": {"name": "A1", "tier": "PBIE_Azure", "capacity": 1},
            "tags": {"env": "dev", "purpose": "releases", "team": "data"},
        },
    )


@pytest.fixture
def pause_capacity(
    responses_mock: responses.RequestsMock,
):
    responses_mock.post(
        "https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity/suspend?api-version=2021-01-01",
    )


@pytest.fixture
def get_details_of_capacity_to_be_paused(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/capacities",
        json={
            "value": [
                {
                    "id": "xxx-xxx-capacity",
                    "displayName": "activecapacity",
                    "admins": ["<EMAIL>"],
                    "sku": "A2",
                    "state": "Active",
                    "capacityUserAccessRight": "Admin",
                    "region": "West Europe",
                    "users": [],
                }
            ],
        },
    )
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/capacities",
        json={
            "value": [
                {
                    "id": "xxx-xxx-capacity",
                    "displayName": "activecapacity",
                    "admins": ["<EMAIL>"],
                    "sku": "A2",
                    "state": "Paused",
                    "capacityUserAccessRight": "Admin",
                    "region": "West Europe",
                    "users": [],
                }
            ],
        },
    )


@pytest.fixture
def resume_capacity(
    responses_mock: responses.RequestsMock,
):
    responses_mock.post(
        "https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/activecapacity/resume?api-version=2021-01-01",
    )


@pytest.fixture
def get_details_capacity_to_be_resumed(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/capacities",
        json={
            "value": [
                {
                    "id": "xxx-xxx-capacity",
                    "displayName": "activecapacity",
                    "admins": ["<EMAIL>"],
                    "sku": "A2",
                    "state": "Paused",
                    "capacityUserAccessRight": "Admin",
                    "region": "West Europe",
                    "users": [],
                }
            ],
        },
    )
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/capacities",
        json={
            "value": [
                {
                    "id": "xxx-xxx-capacity",
                    "displayName": "activecapacity",
                    "admins": ["<EMAIL>"],
                    "sku": "A2",
                    "state": "Active",
                    "capacityUserAccessRight": "Admin",
                    "region": "West Europe",
                    "users": [],
                }
            ],
        },
    )
