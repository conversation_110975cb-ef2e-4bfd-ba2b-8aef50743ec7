import json

import pytest
import responses
from responses import matchers
from sqlalchemy.orm import Session
from starlette.status import HTTP_200_OK, HTTP_404_NOT_FOUND


def test_not_existing_customer_returns_404(
    db_session: Session,
    client,
    authorization_header,
    user_service_legacy_id_lookup_mock,
):
    response = client.get(
        "/dataset/by-user/u-aBcDeF/dashboards-auth-token",
        headers=authorization_header,
    )
    assert response.status_code == HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Studio not found"}


@pytest.fixture
def existing_customer_with_two_shards(
    existing_customer, version_factory, shard_factory
):
    version_factory(id="2.1.0", shard_version="v2")

    shard_factory(
        version="v2",
        dataset_id="44444444-1758-40f1-bf7d-13880a5c3ce5",
        workspace_id="55555555-1758-40f1-bf7d-13880a5c3ce5",
        workspace_name="sample_workspace_name_2",
        capacity_id="66666666-1758-40f1-bf7d-13880a5c3ce5",
    )


@pytest.fixture
def existing_customer_v2_5(
    version_factory, profile_factory, shard_factory, permission_set_factory
):
    version_factory(id="2.5.0")
    profile_factory(active_version_id="2.5.0")
    shard_factory()
    permission_set_factory()


@pytest.fixture
def existing_customer_v2_6(
    version_factory, profile_factory, shard_factory, permission_set_factory
):
    version_factory(id="2.6.0")
    profile_factory(active_version_id="2.6.0")
    shard_factory()
    permission_set_factory()


@pytest.fixture
def first_shard_embed_token_mock(
    power_bi_api: responses.RequestsMock, user_service_legacy_id_lookup_mock
):
    power_bi_api.post(
        url="https://api.powerbi.com/v1.0/myorg/GenerateToken",
        body=json.dumps(
            {
                "token": "first_shard_token",
                "tokenId": "11111111-1111-4c29-af52-619ff93b5c80",
                "expiration": "2018-07-29T17:58:19Z",
            }
        ),
        match=[
            matchers.json_params_matcher(
                {
                    "datasets": [
                        {"id": "11111111-1758-40f1-bf7d-13880a5c3ce5"},
                        {"id": "dataset-1234-5678-123123123"},
                    ],
                    "reports": [
                        {"id": "report01-aaaa-bbbb-cccc-ddddddcccccc"},
                        {"id": "report02-aaaa-bbbb-cccc-ddddddcccccc"},
                        {"id": "report03-aaaa-bbbb-cccc-ddddddcccccc"},
                    ],
                    "lifetimeInMinutes": 0,
                }
            )
        ],
    )


@pytest.fixture
def second_shard_embed_token_mock(power_bi_api: responses.RequestsMock):
    power_bi_api.post(
        url="https://api.powerbi.com/v1.0/myorg/GenerateToken",
        body=json.dumps(
            {
                "token": "second_shard_token",
                "tokenId": "22222222-2222-aaaa-af52-619ff93b5c80",
                "expiration": "2018-07-29T17:58:19Z",
            }
        ),
        match=[
            matchers.json_params_matcher(
                {
                    "datasets": [
                        {"id": "44444444-1758-40f1-bf7d-13880a5c3ce5"},
                        {"id": "dataset-1234-5678-123123123"},
                    ],
                    "reports": [
                        {"id": "report01-aaaa-bbbb-cccc-ddddddcccccc"},
                        {"id": "report02-aaaa-bbbb-cccc-ddddddcccccc"},
                        {"id": "report03-aaaa-bbbb-cccc-ddddddcccccc"},
                    ],
                    "lifetimeInMinutes": 0,
                }
            )
        ],
    )


def test_existing_customer_returns_200(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    first_shard_embed_token_mock,
):
    response = client.get(
        "/dataset/by-user/u-aBcDeF/dashboards-auth-token",
        headers=authorization_header,
    )
    assert response.status_code == HTTP_200_OK


def test_existing_customer_returns_404_for_not_existing_forced_version(
    db_session: Session, client, authorization_header, existing_customer
):
    response = client.get(
        "/dataset/by-user/u-aBcDeF/dashboards-auth-token/version/0.0.1",
        headers=authorization_header,
    )
    assert response.status_code == HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Version not found"}


def test_existing_cusomter_returns_active_shard_dataset_id(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    first_shard_embed_token_mock,
):
    response = client.get(
        "/dataset/by-user/u-aBcDeF/dashboards-auth-token",
        headers=authorization_header,
    )
    embed_token = response.json()

    assert embed_token["dataset_id"] == "11111111-1758-40f1-bf7d-13880a5c3ce5"


def test_existing_cusomter_returns_active_shard_version(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    first_shard_embed_token_mock,
):
    response = client.get(
        "/dataset/by-user/u-aBcDeF/dashboards-auth-token",
        headers=authorization_header,
    )
    embed_token = response.json()

    assert embed_token["version"] == "2.0.4"


def test_existing_cusomter_returns_token_fetched_from_API(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    first_shard_embed_token_mock,
):
    response = client.get(
        "/dataset/by-user/u-aBcDeF/dashboards-auth-token",
        headers=authorization_header,
    )
    embed_token = response.json()

    assert embed_token["token"] == "first_shard_token"


def test_existing_cusomter_returns_tokens_expiration_date_fetched_from_API(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    first_shard_embed_token_mock,
):
    response = client.get(
        "/dataset/by-user/u-aBcDeF/dashboards-auth-token",
        headers=authorization_header,
    )
    embed_token = response.json()

    assert embed_token["expiration_date"] == "2018-07-29T17:58:19Z"


def test_existing_cusomter_returns_hidden_slicers_false_for_version_2_5(
    db_session: Session,
    client,
    authorization_header,
    existing_customer_v2_5,
    first_shard_embed_token_mock,
):
    response = client.get(
        "/dataset/by-user/u-aBcDeF/dashboards-auth-token",
        headers=authorization_header,
    )
    embed_token = response.json()

    assert embed_token["hidden_slicers"] is False


def test_existing_cusomter_returns_hidden_slicers_true_for_version_2_6(
    db_session: Session,
    client,
    authorization_header,
    existing_customer_v2_6,
    first_shard_embed_token_mock,
):
    response = client.get(
        "/dataset/by-user/u-aBcDeF/dashboards-auth-token",
        headers=authorization_header,
    )
    embed_token = response.json()

    assert embed_token["hidden_slicers"] is True


def test_existing_cusomter_returns_forced_version(
    db_session: Session,
    client,
    authorization_header,
    existing_customer_with_two_shards,
    second_shard_embed_token_mock,
):
    response = client.get(
        "/dataset/by-user/u-aBcDeF/dashboards-auth-token/version/2.1.0",
        headers=authorization_header,
    )
    embed_token = response.json()

    assert embed_token["dataset_id"] == "44444444-1758-40f1-bf7d-13880a5c3ce5"
    assert embed_token["version"] == "2.1.0"
    assert (
        embed_token["security_key_value"] == '[{"studio_id": 1, "product_name": null}]'
    )
