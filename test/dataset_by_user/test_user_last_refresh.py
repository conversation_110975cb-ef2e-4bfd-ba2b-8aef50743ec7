from datetime import datetime

import pytest
import responses
from dateutil.tz import tzutc
from sqlalchemy.orm import Session
from starlette.status import HTTP_200_OK, HTTP_404_NOT_FOUND

from dataset_manager.azure.powerbi.datasets import DatasetAPI


@pytest.fixture
def permission_set_1_last_refresh_failed(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/22222222-1758-40f1-bf7d-13880a5c3ce5/datasets/11111111-1758-40f1-bf7d-13880a5c3ce5/refreshes",
        json={
            "value": [
                {
                    "requestId": "d0fb865e-9086-4416-8141-896872f4b959",
                    "id": 278313963,
                    "refreshType": "ViaEnhancedApi",
                    "startTime": "2023-06-21T09:02:22.34Z",
                    "endTime": "2023-06-21T09:02:47.647Z",
                    "status": "Failed",
                    "extendedStatus": "Failed",
                },
                {
                    "requestId": "a9be315f-3d87-45c6-91d2-8c34ce9dd0e6",
                    "id": 278308539,
                    "refreshType": "ViaEnhancedApi",
                    "startTime": "2023-06-21T08:40:51.633Z",
                    "endTime": "2023-06-21T08:41:15.853Z",
                    "status": "Completed",
                    "extendedStatus": "Completed",
                },
                {
                    "requestId": "563c6179-c7f4-4f16-9293-767de3482269",
                    "id": 278302680,
                    "refreshType": "ViaEnhancedApi",
                    "startTime": "2023-06-21T08:06:25.75Z",
                    "endTime": "2023-06-21T08:06:54.227Z",
                    "status": "Completed",
                    "extendedStatus": "Completed",
                },
            ]
        },
    )


@pytest.fixture
def permission_set_1_last_refresh_completed(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/22222222-1758-40f1-bf7d-13880a5c3ce5/datasets/11111111-1758-40f1-bf7d-13880a5c3ce5/refreshes",
        json={
            "value": [
                {
                    "requestId": "d0fb865e-9086-4416-8141-896872f4b959",
                    "id": 278313963,
                    "refreshType": "ViaEnhancedApi",
                    "startTime": "2023-06-21T09:02:22.34Z",
                    "endTime": "2023-06-21T09:02:47.647Z",
                    "status": "Completed",
                    "extendedStatus": "Completed",
                },
                {
                    "requestId": "a9be315f-3d87-45c6-91d2-8c34ce9dd0e6",
                    "id": 278308539,
                    "refreshType": "ViaEnhancedApi",
                    "startTime": "2023-06-21T08:40:51.633Z",
                    "endTime": "2023-06-21T08:41:15.853Z",
                    "status": "Completed",
                    "extendedStatus": "Completed",
                },
                {
                    "requestId": "563c6179-c7f4-4f16-9293-767de3482269",
                    "id": 278302680,
                    "refreshType": "ViaEnhancedApi",
                    "startTime": "2023-06-21T08:06:25.75Z",
                    "endTime": "2023-06-21T08:06:54.227Z",
                    "status": "Completed",
                    "extendedStatus": "Completed",
                },
            ]
        },
    )


@pytest.fixture
def permission_set_1_last_refresh_ongoing(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/22222222-1758-40f1-bf7d-13880a5c3ce5/datasets/11111111-1758-40f1-bf7d-13880a5c3ce5/refreshes",
        json={
            "value": [
                {
                    "requestId": "d0fb865e-9086-4416-8141-896872f4b959",
                    "id": 278313963,
                    "refreshType": "ViaEnhancedApi",
                    "startTime": "2023-06-21T09:02:22.34Z",
                    "endTime": "2023-06-21T09:02:47.647Z",
                    "status": "Unknown",
                    "extendedStatus": "Unknown",
                },
                {
                    "requestId": "a9be315f-3d87-45c6-91d2-8c34ce9dd0e6",
                    "id": 278308539,
                    "refreshType": "ViaEnhancedApi",
                    "startTime": "2023-06-21T08:40:51.633Z",
                    "endTime": "2023-06-21T08:41:15.853Z",
                    "status": "Completed",
                    "extendedStatus": "Completed",
                },
                {
                    "requestId": "563c6179-c7f4-4f16-9293-767de3482269",
                    "id": 278302680,
                    "refreshType": "ViaEnhancedApi",
                    "startTime": "2023-06-21T08:06:25.75Z",
                    "endTime": "2023-06-21T08:06:54.227Z",
                    "status": "Completed",
                    "extendedStatus": "Completed",
                },
            ]
        },
    )


@pytest.fixture
def permission_set_1_no_refreshes(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/22222222-1758-40f1-bf7d-13880a5c3ce5/datasets/11111111-1758-40f1-bf7d-13880a5c3ce5/refreshes",
        json={"value": []},
    )


def test_last_refresh_if_last_refresh_failed(
    db_session: Session,
    dataset_api: DatasetAPI,
    permission_set_1_last_refresh_failed,
    shard_factory,
):
    shard = shard_factory()
    print(shard.workspace_id, shard.dataset_id)
    last_refresh = dataset_api.last_successful_refresh(
        shard.workspace_id, shard.dataset_id
    )

    assert last_refresh == datetime(2023, 6, 21, 8, 41, 15, 853000, tzinfo=tzutc())


def test_last_refresh_if_last_refresh_completed(
    db_session: Session,
    dataset_api: DatasetAPI,
    permission_set_1_last_refresh_completed,
    shard_factory,
):
    shard = shard_factory()
    last_refresh = dataset_api.last_successful_refresh(
        shard.workspace_id, shard.dataset_id
    )
    assert last_refresh == datetime(2023, 6, 21, 9, 2, 47, 647000, tzinfo=tzutc())


def test_last_refresh_if_last_refresh_ongoing(
    db_session: Session,
    dataset_api: DatasetAPI,
    permission_set_1_last_refresh_ongoing,
    shard_factory,
):
    shard = shard_factory()
    last_refresh = dataset_api.last_successful_refresh(
        shard.workspace_id, shard.dataset_id
    )
    assert last_refresh == datetime(2023, 6, 21, 8, 41, 15, 853000, tzinfo=tzutc())


def test_last_refresh_if_no_refreshes(
    db_session: Session,
    dataset_api: DatasetAPI,
    permission_set_1_no_refreshes,
    shard_factory,
):
    shard = shard_factory()
    last_refresh = dataset_api.last_successful_refresh(
        shard.workspace_id, shard.dataset_id
    )
    assert last_refresh is None


def test_not_existing_customer_returns_404_last_refresh(
    db_session: Session, client, authorization_header
):
    response = client.get(
        "/studio/1/last-refresh",
        headers=authorization_header,
    )
    assert response.status_code == HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Studio not found"}


@pytest.fixture
def existing_customer_no_last_refresh(
    version_factory, profile_factory, shard_factory, permission_set_factory
):
    version_factory()
    profile_factory()
    shard_factory(last_refresh_timestamp=None)
    permission_set_factory()


def test_existing_customer_returns_last_refresh_date(
    db_session: Session,
    client,
    authorization_header,
    existing_customer_no_last_refresh,
    permission_set_1_last_refresh_completed,
):
    response = client.get(
        "/studio/1/last-refresh",
        headers=authorization_header,
    )
    assert response.status_code == HTTP_200_OK
    assert response.json() == "2023-06-21T09:02:47.647000+00:00"


def test_last_refresh_is_saved_in_db(
    db_session: Session,
    client,
    authorization_header,
    existing_customer_no_last_refresh,
    permission_set_1_last_refresh_completed,
):
    response = client.get(
        "/studio/1/shard/active",
        headers=authorization_header,
    )
    assert response.json()["last_refresh_timestamp"] is None

    response = client.get(
        "/studio/1/last-refresh",
        headers=authorization_header,
    )
    assert response.json() == "2023-06-21T09:02:47.647000+00:00"

    response = client.get(
        "/studio/1/shard/active",
        headers=authorization_header,
    )
    assert response.status_code == HTTP_200_OK
    assert response.json()["last_refresh_timestamp"] == "2023-06-21T09:02:47.647000"
