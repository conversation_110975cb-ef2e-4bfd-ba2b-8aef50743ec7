from sqlalchemy.orm import Session

from dataset_manager.repo.permission_set import _DBPermissionSet
from dataset_manager.repo.profile import _DBProfile
from dataset_manager.repo.release import ReleaseStatus, _DBRelease


def test_create_permission_set_saves_permission_with_md5_hash(
    db_session: Session, client, authorization_header, profile_factory
):
    profile_factory(permission_set_uuid=None)

    client.post(
        "/studio/1/permission-set",
        headers=authorization_header,
        json={"permission_set": [{"studio_id": 10, "product_name": None}]},
    )

    all_permission_sets = db_session.query(_DBPermissionSet).all()

    assert len(all_permission_sets) == 1, all_permission_sets

    added_permission_set = all_permission_sets[0]
    assert added_permission_set.permission_set == [
        {"studio_id": 10, "product_name": None}
    ]
    assert (
        added_permission_set.permission_set_hash == "45c2505d5a942c91833e24215bdf7e08"
    )


def test_create_permission_doesnt_assigns_perrmisions_set_instantly_to_existing_profile(
    db_session: Session, client, authorization_header, profile_factory
):
    profile_factory(permission_set_uuid=None)

    client.post(
        "/studio/1/permission-set",
        headers=authorization_header,
        json={"permission_set": [{"studio_id": 10, "product_name": None}]},
    )

    all_profiles = db_session.query(_DBProfile).all()
    assert len(all_profiles) == 1, all_profiles
    updated_profile = all_profiles[0]

    assert updated_profile.permission_set_uuid is None


def test_create_unique_permission(
    db_session: Session,
    client,
    authorization_header,
    version_factory,
    permission_set_factory,
    profile_factory,
):
    version_factory()
    permission_set_factory()
    profile_factory(studio_id=1)
    profile_factory(studio_id=2, permission_set_uuid=None)

    response = client.post(
        "/studio/2/permission-set",
        headers=authorization_header,
        json={"permission_set": [{"studio_id": 1, "product_name": None}]},
    )
    all_permission_sets = db_session.query(_DBPermissionSet).all()

    assert len(all_permission_sets) == 1, "There should be only one permission set"
    assert response.json()["uuid"] == "eeeeeee1-7d79-4403-83e9-916b65129739"

    all_profiles = db_session.query(_DBProfile).all()
    assert len(all_profiles) == 2, all_profiles
    assert {
        (profile.studio_id, profile.permission_set_uuid) for profile in all_profiles
    } == {(1, "eeeeeee1-7d79-4403-83e9-916b65129739"), (2, None)}


def test_create_permission_set_are_sorted_by_studio_and_product(
    db_session: Session, client, authorization_header, profile_factory
):
    profile_factory(permission_set_uuid=None)

    client.post(
        "/studio/1/permission-set",
        headers=authorization_header,
        json={
            "permission_set": [
                {"studio_id": 3, "product_name": None},
                {"studio_id": 1, "product_name": None},
                {"studio_id": 2, "product_name": "BBBB"},
                {"studio_id": 2, "product_name": "AAAA"},
            ]
        },
    )

    added_permission_set = db_session.query(_DBPermissionSet).all()[0]
    assert added_permission_set.permission_set == [
        {"studio_id": 1, "product_name": None},
        {"studio_id": 2, "product_name": "AAAA"},
        {"studio_id": 2, "product_name": "BBBB"},
        {"studio_id": 3, "product_name": None},
    ]


def test_create_permissions_saves_release_information_for_new_studio_with_default_version(
    db_session: Session, client, authorization_header, version_factory
):
    version_factory(id="2.2.0", is_default=False)
    version_factory(id="2.3.0")
    version_factory(id="2.4.0", is_default=False)

    response = client.post(
        "/studio/2/permission-set",
        headers=authorization_header,
        json={"permission_set": [{"studio_id": 1, "product_name": None}]},
    )

    all_releases = db_session.query(_DBRelease).all()
    release = all_releases[0]
    assert release.studio_id == 2
    assert release.version_id == "2.3.0"
    assert release.permission_set_uuid == response.json()["uuid"]
    assert release.status == ReleaseStatus.REQUESTED


def test_create_permissions_for_profile_with_existing_permission_set_skips_release_create_call(
    db_session: Session,
    client,
    authorization_header,
    profile_factory,
    version_factory,
    permission_set_factory,
):
    profile_factory(active_version_id="2.3.3")
    permission_set_factory()
    version_factory(id="2.3.3")

    client.post(
        "/studio/1/permission-set",
        headers=authorization_header,
        json={"permission_set": [{"studio_id": 1, "product_name": None}]},
    )

    all_releases = db_session.query(_DBRelease).all()
    assert all_releases == []

    all_permission_sets = db_session.query(_DBPermissionSet).all()
    assert len(all_permission_sets) == 1


def test_create_permissions_for_profile_with_existing_permission_set_runs_new_release_when_permission_set_differs(
    db_session: Session,
    client,
    authorization_header,
    profile_factory,
    version_factory,
    permission_set_factory,
):
    profile_factory(active_version_id="2.3.3")
    permission_set_factory()
    version_factory(id="2.3.3")

    response = client.post(
        "/studio/1/permission-set",
        headers=authorization_header,
        json={"permission_set": [{"studio_id": 1, "product_name": "SUPERHOT"}]},
    )

    all_releases = db_session.query(_DBRelease).all()
    release = all_releases[0]
    assert len(all_releases) == 1
    assert release.studio_id == 1
    assert release.version_id == "2.3.3"
    assert release.permission_set_uuid == response.json()["uuid"]
    assert release.status == ReleaseStatus.REQUESTED

    all_permission_sets = (
        db_session.query(_DBPermissionSet)
        .order_by(_DBPermissionSet.permission_set)
        .all()
    )
    assert len(all_permission_sets) == 2
    assert all_permission_sets[1].permission_set == [
        {"studio_id": 1, "product_name": None},
    ]
    assert all_permission_sets[0].permission_set == [
        {"studio_id": 1, "product_name": "SUPERHOT"},
    ]


def test_mark_undone_release_as_outdated_when_new_permission_set_is_created_to_the_profile(
    db_session: Session,
    client,
    authorization_header,
    profile_factory,
    version_factory,
    permission_set_factory,
    release_factory,
):
    profile_factory(active_version_id="2.3.3")
    permission_set_factory()
    version_factory(id="2.3.3")
    release_factory(
        studio_id=1,
        version_id="2.3.3",
        permission_set_uuid="eeeeeee1-7d79-4403-83e9-916b65129739",
    )

    response = client.post(
        "/studio/1/permission-set",
        headers=authorization_header,
        json={"permission_set": [{"studio_id": 1, "product_name": "SUPERHOT"}]},
    )

    all_releases = sorted(
        db_session.query(_DBRelease).all(), key=lambda r: r.creation_timestamp
    )

    assert len(all_releases) == 2

    assert all_releases[0].studio_id == 1
    assert all_releases[0].version_id == "2.3.3"
    assert all_releases[0].permission_set_uuid == "eeeeeee1-7d79-4403-83e9-916b65129739"
    assert all_releases[0].status == ReleaseStatus.OUTDATED

    assert all_releases[1].studio_id == 1
    assert all_releases[1].version_id == "2.3.3"
    assert all_releases[1].permission_set_uuid == response.json()["uuid"]
    assert all_releases[1].status == ReleaseStatus.REQUESTED
