import logging

import pytest
import responses
from sqlalchemy.orm import Session


@pytest.fixture
def get_details_of_capacity_pbi_client_max_retry_exceeded(
    responses_mock: responses.RequestsMock,
    capacity_name: str = "activecapacity",
):
    responses_mock.get(
        f"https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/{capacity_name}?api-version=2021-01-01",
        status=502,
    )
    responses_mock.get(
        f"https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/{capacity_name}?api-version=2021-01-01",
        status=502,
    )
    responses_mock.get(
        f"https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/{capacity_name}?api-version=2021-01-01",
        status=502,
    )
    responses_mock.get(
        f"https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/{capacity_name}?api-version=2021-01-01",
        status=502,
    )


@pytest.fixture
def get_details_of_capacity_pbi_client_retry_success(
    responses_mock: responses.RequestsMock,
    capacity_name: str = "activecapacity",
    capacity_tier: str = "A2",
    state: str = "Succeeded",
):
    responses_mock.get(
        f"https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/{capacity_name}?api-version=2021-01-01",
        status=502,
    )
    responses_mock.get(
        f"https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/{capacity_name}?api-version=2021-01-01",
        json={
            "properties": {
                "mode": "Gen2",
                "provisioningState": "Succeeded",
                "state": state,
                "administration": {"members": ["<EMAIL>"]},
            },
            "id": f"/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/{capacity_name}",
            "name": capacity_name,
            "type": "Microsoft.PowerBIDedicated/capacities",
            "location": "West Europe",
            "sku": {"name": capacity_tier, "tier": "PBIE_Azure", "capacity": 1},
            "tags": {"env": "dev", "purpose": "releases", "team": "data"},
        },
    )


@pytest.fixture
def get_details_of_capacity_pbi_service_max_retry_exceeded(
    responses_mock: responses.RequestsMock, get_management_details_of_capacity
):
    get_management_details_of_capacity()
    responses_mock.get("https://api.powerbi.com/v1.0/myorg/capacities", status=502)
    responses_mock.get("https://api.powerbi.com/v1.0/myorg/capacities", status=502)
    responses_mock.get("https://api.powerbi.com/v1.0/myorg/capacities", status=502)
    responses_mock.get("https://api.powerbi.com/v1.0/myorg/capacities", status=502)


@pytest.fixture
def get_details_of_capacity_pbi_service_retry_success(
    responses_mock: responses.RequestsMock, get_management_details_of_capacity
):
    get_management_details_of_capacity()
    responses_mock.get("https://api.powerbi.com/v1.0/myorg/capacities", status=502)
    responses_mock.get("https://api.powerbi.com/v1.0/myorg/capacities", status=502)
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/capacities",
        json={
            "value": [
                {
                    "id": "xxx-xxx-capacity",
                    "displayName": "activecapacity",
                    "admins": ["<EMAIL>"],
                    "sku": "A2",
                    "state": "Active",
                    "capacityUserAccessRight": "Admin",
                    "region": "West Europe",
                    "users": [],
                }
            ],
        },
    )


@pytest.fixture
def get_details_of_capacity(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/capacities",
        json={
            "value": [
                {
                    "id": "xxx-xxx-capacity",
                    "displayName": "activecapacity",
                    "admins": ["<EMAIL>"],
                    "sku": "A2",
                    "state": "Active",
                    "capacityUserAccessRight": "Admin",
                    "region": "West Europe",
                    "users": [],
                }
            ],
        },
    )


def test_azure_api_pbi_client_retry_mechanism_exceeded(
    db_session: Session,
    client,
    authorization_header,
    get_details_of_capacity_pbi_client_max_retry_exceeded,
    power_bi_api,
    caplog,
):
    with caplog.at_level(logging.ERROR, logger="dataset_manager.main"):
        response = client.post(
            "/capacity",
            headers=authorization_header,
            json={"capacity_name": "activecapacity"},
        )
    assert response.json()["detail"] == "Oops! Error calling external services"
    assert (
        "While calling http://testserver/capacity there was an error Expecting value: line 1 column 1 (char 0) with response: Unknown"
        in caplog.text
    )


def test_azure_api_pbi_service_retry_mechanism_exceeded(
    db_session: Session,
    client,
    authorization_header,
    get_details_of_capacity_pbi_service_max_retry_exceeded,
    power_bi_api,
    caplog,
):
    with caplog.at_level(logging.ERROR, logger="dataset_manager.main"):
        response = client.post(
            "/capacity",
            headers=authorization_header,
            json={"capacity_name": "activecapacity"},
        )
    assert response.json()["detail"] == "Oops! Error calling external services"
    assert (
        "While calling http://testserver/capacity there was an error 502 Server Error"
        in caplog.text
    )


def test_azure_api_pbi_service_retry_mechanism_success(
    db_session: Session,
    client,
    authorization_header,
    get_details_of_capacity_pbi_service_retry_success,
    power_bi_api,
):
    response = client.post(
        "/capacity",
        headers=authorization_header,
        json={"capacity_name": "activecapacity"},
    )

    capacity = response.json()
    assert capacity["id"] == "xxx-xxx-capacity"
    assert capacity["name"] == "activecapacity"


def test_azure_api_pbi_client_retry_mechanism_success(
    db_session: Session,
    client,
    authorization_header,
    get_details_of_capacity_pbi_client_retry_success,
    get_details_of_capacity,
    power_bi_api,
):
    response = client.post(
        "/capacity",
        headers=authorization_header,
        json={"capacity_name": "activecapacity"},
    )

    capacity = response.json()
    assert capacity["id"] == "xxx-xxx-capacity"
    assert capacity["name"] == "activecapacity"
