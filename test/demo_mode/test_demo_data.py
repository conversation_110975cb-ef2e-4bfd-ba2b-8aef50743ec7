import pytest
from starlette.status import HTTP_200_OK


@pytest.fixture
def demo_dim_sku_query_mock(respx_mock):
    query = """
            EVALUATE SELECTCOLUMNS(dim_sku,
            "unique_sku_id", [unique_sku_id],
            "base_sku_id", [base_sku_id],
            "portal_platform_region", [portal_platform_region],
            "gso", [gso],
            "human_name", [human_name],
            "product_name", RELATED(dim_products[product_name]),
            "sku_type", [sku_type],
            "studio_id", RELATED(dim_products[studio_id]),
            "product_id", [product_id],
            "release_date", [release_date])
            """
    respx_mock.post(
        url="https://api.powerbi.com/v1.0/myorg/groups/9c0778ee-6f7c-4e4f-b1ed-294f90bc7887/datasets/6168dab1-9521-4bca-9f09-504edd812f40/executeQueries",
        json={
            "queries": [{"query": query}],
            "serializerSettings": {"includeNulls": True},
        },
    ).respond(
        json={
            "results": [
                {
                    "tables": [
                        {
                            "rows": [
                                {
                                    "[unique_sku_id]": "42ccb715787e47f48648159060135ef1-epic:2",
                                    "[base_sku_id]": "42ccb715787e47f48648159060135ef1",
                                    "[portal_platform_region]": "Epic:PC:Global",
                                    "[gso]": 11,
                                    "[human_name]": "SUPERHOT",
                                    "[product_name]": "SUPERHOT",
                                    "[sku_type]": "SALES",
                                    "[studio_id]": 2,
                                    "[product_id]": "SUPERHOT:101010:2",
                                    "[release_date]": "2014-06-14T00:00:00",
                                },
                                {
                                    "[unique_sku_id]": "942e9424124145fca7fadefefb9b70e6-epic:2",
                                    "[base_sku_id]": "942e9424124145fca7fadefefb9b70e6",
                                    "[portal_platform_region]": "Epic:PC:Global",
                                    "[gso]": 12,
                                    "[human_name]": "SUPERHOT: Mind Control Delete",
                                    "[product_name]": "SUPERHOT MCD",
                                    "[sku_type]": "SALES",
                                    "[studio_id]": 2,
                                    "[product_id]": "SUPERHOT MCD:101010:2",
                                    "[release_date]": "2014-06-14T00:00:00",
                                },
                            ]
                        }
                    ]
                }
            ]
        }
    )


def test_existing_customer_returns_sku_using_cache(
    respx_mock,
    authorized_client,
    demo_dim_sku_query_mock,
):
    response = authorized_client.get("/demo-mode/sku")
    assert response.status_code == HTTP_200_OK
    assert response.json() == [
        {
            "unique_sku_id": "42ccb715787e47f48648159060135ef1-epic:2",
            "base_sku_id": "42ccb715787e47f48648159060135ef1",
            "portal_platform_region": "Epic:PC:Global",
            "gso": 11,
            "name": "SUPERHOT",
            "sku_type": "SALES",
            "studio_id": 2,
            "product_id": "SUPERHOT:101010:2",
            "product_name": "SUPERHOT",
            "release_date": "2014-06-14T00:00:00",
        },
        {
            "unique_sku_id": "942e9424124145fca7fadefefb9b70e6-epic:2",
            "base_sku_id": "942e9424124145fca7fadefefb9b70e6",
            "portal_platform_region": "Epic:PC:Global",
            "gso": 12,
            "name": "SUPERHOT: Mind Control Delete",
            "sku_type": "SALES",
            "studio_id": 2,
            "product_id": "SUPERHOT MCD:101010:2",
            "product_name": "SUPERHOT MCD",
            "release_date": "2014-06-14T00:00:00",
        },
    ]
    response = authorized_client.get("/demo-mode/sku")

    respx_mock.calls.assert_called_once()
