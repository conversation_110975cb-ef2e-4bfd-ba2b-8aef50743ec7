import json

import pytest
import responses
from responses import matchers

from dataset_manager.config import DemoModeSettings, Settings, get_modern_config
from dataset_manager.main import app


@pytest.fixture
def demo_mode_settings():
    old_deps = app.dependency_overrides.copy()

    demo_mode = DemoModeSettings(
        dateset_id="11111111-1758-40f1-bf7d-13880a5c3ce5",
        reports_ids=[
            "report01-aaaa-bbbb-cccc-ddddddcccccc",
            "report02-aaaa-bbbb-cccc-ddddddcccccc",
            "report03-aaaa-bbbb-cccc-ddddddcccccc",
        ],
    )

    app.dependency_overrides[get_modern_config] = lambda: Settings(demo_mode=demo_mode)

    yield demo_mode

    app.dependency_overrides = old_deps


@pytest.fixture
def embed_token_mock(power_bi_api: responses.RequestsMock):
    power_bi_api.post(
        url="https://api.powerbi.com/v1.0/myorg/GenerateToken",
        body=json.dumps(
            {
                "token": "demo_mode_shard_token",
                "tokenId": "11111111-1111-4c29-af52-619ff93b5c80",
                "expiration": "2018-07-29T17:58:19Z",
            }
        ),
        match=[
            matchers.json_params_matcher(
                {
                    "datasets": [
                        {"id": "11111111-1758-40f1-bf7d-13880a5c3ce5"},
                    ],
                    "reports": [
                        {"id": "report01-aaaa-bbbb-cccc-ddddddcccccc"},
                        {"id": "report02-aaaa-bbbb-cccc-ddddddcccccc"},
                        {"id": "report03-aaaa-bbbb-cccc-ddddddcccccc"},
                    ],
                    "lifetimeInMinutes": 0,
                }
            )
        ],
    )


def test_generate_demo_mode_token(
    authorized_client, embed_token_mock, demo_mode_settings
):
    response = authorized_client.get("/demo-mode/dashboards-auth-token")

    assert response.status_code == 200
    assert response.json() == {
        "token": "demo_mode_shard_token",
        "expiration_date": "2018-07-29T17:58:19Z",
        "dataset_id": "11111111-1758-40f1-bf7d-13880a5c3ce5",
        "version": "3.4.1",
    }
