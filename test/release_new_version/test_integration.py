# TODO: make some inegration test for release

# def test_initialize_next_release_if_shard_refresh_is_pending(
#     db_session: Session,
#     repo: Repo,
#     version_factory,
#     profile_factory,
#     shard_factory,
#     release_factory,
#     permission_set_factory,
#     capacity_factory,
#     power_bi_api,
#     capacieties_api: CapacietiesAPI,
#     workspace_api: WorkspaceAPI,
#     dataset_api: DatasetAPI,
#     get_management_details_of_capacity,
#     permission_set_2_dataset_refresh_is_pending,
#     create_not_existing_workspace_permission_set_1,
#     asign_additional_admins_to_permission_set_1_shard_workspace,
#     asign_temporary_capacity_to_permission_set_1_workspace,
#     deploy_permission_set_1_dataset_from_file,
#     get_uploaded_permission_set_1_dataset_id_with_retry,
#     take_ownership_to_permission_set_1_dataset,
#     update_permission_in_permission_set_1_dataset,
#     update_adls_url_in_permission_set_1_dataset,
#     verify_permission_set_1_in_dataset,
#     verify_adls_url_1_in_dataset,
#     set_credentials_for_permission_set_1_datasource,
#     refresh_permission_set_1_workspace_dataset,
# ):
#     version_factory(
#         id="1.0.0",
#         shard_version="1.0.0",
#         is_default=True,
#     )
#     version_factory(
#         id="2.3.0",
#         shard_version="2.3.0",
#         visuals_workspace_id="main-workspace-xyz-xyz-12312312312",
#         is_default=False,
#     )
#     permission_set_factory()
#     second_permission_set = permission_set_factory()
#     release_factory(
#         studio_id=2, version_id="2.3.0", permission_set_uuid=second_permission_set.uuid
#     )
#     release_factory(studio_id=1, version_id="2.3.0")

#     profile_factory(
#         studio_id=2,
#         active_version_id="1.0.0",
#         profile_id="profile2-zxcc-555-hhhh-efesdvsdvdf",
#     )
#     shard_factory(
#         permission_set_uuid=second_permission_set.uuid,
#         version="1.0.0",
#         workspace_id="workspace2-old2-dddd-dddd-dddddddddddd",
#     )
#     shard_factory(
#         permission_set_uuid=second_permission_set.uuid,
#         version="2.3.0",
#         workspace_id="workspace2-ps2-4e18-aea3-4cb4a3a50b48",
#         dataset_id="dataset_permission_set_2-8037-a46fb27",
#         capacity_id="capacity-temporary-c13d-451b-af5f-ed0c46",
#     )

#     profile_factory(
#         studio_id=1,
#         active_version_id="1.0.0",
#         profile_id="profile1-54b7-43f4-b072-ed4c1f9d5824",
#     )
#     shard_factory(version="1.0.0")
#     capacity_factory(
#         id="capacity-temporary-c13d-451b-af5f-ed0c46",
#         name="temporary_capacity_name",
#         is_default_for_releases=True,
#     )
#     get_management_details_of_capacity(capacity_name="temporary_capacity_name")
#     capacity_factory(
#         id="capacity-default-c13d-451b-af5f-ed0c4664",
#         name="embed_capacity_name",
#         is_default=True,
#     )

#     environment = Environment("local")
#     processed_adls_credentials = ProcessedAdlsCredentials("processed_adls_credential")
#     service_principal_id = ServicePrincipalId("mainprincipal-123-123-123-33333333")
#     additional_workspace_admins = [
#         {
#             "groupUserAccessRight": "Admin",
#             "displayName": "Some additional principal",
#             "identifier": "principal1-2222-2222-2222-222222222222",
#             "principalType": "App",
#             "emailAddress": "",
#         }
#     ]

#     assign_releases(repo=repo, capacieties_api=capacieties_api)
#     for release in repo.release.requested_shard(limit=100):
#         handle_requested_shard(
#             repo=repo,
#             workspace_api=workspace_api,
#             dataset_api=dataset_api,
#             environment=environment,
#             processed_adls_credentials=processed_adls_credentials,
#             additional_workspace_admins=additional_workspace_admins,
#             release=release,
#         )

#     for release in repo.release.requested_assign(limit=100):
#         handle_requested_asign(
#             repo=repo,
#             workspace_api=workspace_api,
#             environment=environment,
#             service_principal_id=service_principal_id,
#             release=release,
#         )

#     all_shards = db_session.query(_DBShard).all()
#     assert len(all_shards) == 4, all_shards

#     pending_shard = all_shards[1]
#     assert pending_shard.version == "2.3.0"
#     assert pending_shard.capacity_id == "capacity-temporary-c13d-451b-af5f-ed0c46"

#     pending_studio_profile = (
#         db_session.query(_DBProfile).filter(_DBProfile.studio_id == 2).one()
#     )
#     assert pending_studio_profile.active_version_id == "1.0.0"

#     released_shard = all_shards[3]
#     assert released_shard.version == "2.3.0"
#     assert released_shard.capacity_id == "capacity-temporary-c13d-451b-af5f-ed0c46"

#     released_studio_profile = (
#         db_session.query(_DBProfile).filter(_DBProfile.studio_id == 1).one()
#     )
#     assert released_studio_profile.active_version_id == "1.0.0"

#     all_releases = db_session.query(_DBRelease).all()
#     assert all_releases[0].status == ReleaseStatus.DONE
#     assert all_releases[1].status == ReleaseStatus.DONE
