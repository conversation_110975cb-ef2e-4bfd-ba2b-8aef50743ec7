import random

from sqlalchemy.orm import Session

from dataset_manager.azure.powerbi.workspaces import WorkspaceAP<PERSON>
from dataset_manager.logic.shard import assing_to_best_capacity
from dataset_manager.repo import Repo
from dataset_manager.repo.shard import Shard, _DBShard


def test_assing_to_best_capacity_selects_random_capacity(
    db_session: Session,
    repo: Repo,
    workspace_api: WorkspaceAPI,
    capacity_factory,
    shard_factory,
    power_bi_api,
    asign_random_capacity_to_permission_set_1_workspace,
):
    random.seed("abc")
    capacity_factory(
        id="capacity-default-c13d-451b-af5f-ed0c4664",
        name="embed_capacity_name",
        is_default=True,
    )
    capacity_factory(
        id="capacity-nodeff1-c13d-451b-af5f-ed0c4664",
        name="embed_capacity_name2",
        is_default=False,
    )
    capacity_factory(
        id="capacity-nodeff2-c13d-451b-af5f-ed0c4664",
        name="embed_capacity_name3",
        is_default=False,
    )
    shard = shard_factory(
        dataset_id="dataset_permission_set_1-8037-a46fb27",
        workspace_id="workspace1-ps1-4e18-aea3-4cb4a3a50b48",
        capacity_id="capacity-temporary-c13d-451b-af5f-ed0c46",
        version="2.3.0",
        workspace_name="local_2.3.0_eeeeeee1-7d79-4403-83e9-916b65129739",
        permission_set_uuid="eeeeeee1-7d79-4403-83e9-916b65129739",
    )
    assing_to_best_capacity(
        repo=repo, workspace_api=workspace_api, shard=Shard.model_validate(shard)
    )
    assert shard.capacity_id == "capacity-nodeff2-c13d-451b-af5f-ed0c4664"

    all_shards_created = db_session.query(_DBShard).all()
    shard = all_shards_created[0]

    assert shard.capacity_id == "capacity-nodeff2-c13d-451b-af5f-ed0c4664"

    assert len(all_shards_created) == 1
