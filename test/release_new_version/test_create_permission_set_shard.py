from unittest.mock import Magic<PERSON>ock

import pytest
import responses
from sqlalchemy.orm import Session

from dataset_manager.azure.powerbi.datasets import DatasetAPI
from dataset_manager.azure.powerbi.workspaces import WorkspaceAPI
from dataset_manager.entities import (
    Environment,
    ProcessedAdlsCredentials,
    ProcessedAdlsURL,
    ShardVersion,
)
from dataset_manager.logic.shard import (
    CreatePermissionSetShardRequest,
    RefreshFailed,
    RefreshTooHeavyPermissionSet,
    create_permission_set_shard,
)
from dataset_manager.repo import Repo
from dataset_manager.repo.shard import _DBShard


def test_create_new_permission_set_shard_from_blob(
    db_session: Session,
    repo: Repo,
    workspace_api: WorkspaceAPI,
    dataset_api: DatasetAPI,
    permission_set_factory,
    capacity_factory,
    model_container,
    create_not_existing_workspace_permission_set_1,
    asign_temporary_capacity_to_permission_set_1_workspace,
    prepare_model_on_blob,
    deploy_permission_set_1_dataset_from_blob,
    get_uploaded_permission_set_1_dataset_id_with_retry,
    take_ownership_to_permission_set_1_dataset,
    update_permission_in_permission_set_1_dataset,
    update_adls_url_in_permission_set_1_dataset,
    verify_permission_set_1_in_dataset,
    verify_adls_url_1_in_dataset,
    set_credentials_for_permission_set_1_datasource,
    refresh_permission_set_1_workspace_dataset,
    permission_set_1_dataset_refresh_has_finished,
):
    permission_set = permission_set_factory()
    capacity_factory(
        id="capacity-temporary-c13d-451b-af5f-ed0c46",
        name="temporary_capacity_name",
        is_default_for_releases=True,
    )

    create_permission_set_shard(
        repo=repo,
        workspace_api=workspace_api,
        dataset_api=dataset_api,
        model_container=model_container,
        request=CreatePermissionSetShardRequest(
            permission_set_uuid=permission_set.uuid,
            shard_version=ShardVersion("2.5.0"),
            environment=Environment("local"),
            processed_adls_url=ProcessedAdlsURL("https://test.adls-url.not-existing"),
            processed_adls_credentials=ProcessedAdlsCredentials(
                "processed_adls_credential"
            ),
        ),
    )
    all_shards_created = db_session.query(_DBShard).all()
    shard = all_shards_created[0]

    assert shard.dataset_id == "dataset_permission_set_1-8037-a46fb27"
    assert shard.workspace_id == "workspace1-ps1-4e18-aea3-4cb4a3a50b48"
    assert shard.capacity_id == "capacity-temporary-c13d-451b-af5f-ed0c46"
    assert shard.version == "2.5.0"

    assert shard.dataset_name is None
    assert shard.workspace_name == "local_2.5.0_eeeeeee1-7d79-4403-83e9-916b65129739"
    assert shard.permission_set_uuid == "eeeeeee1-7d79-4403-83e9-916b65129739"

    assert len(all_shards_created) == 1


def test_create_new_permission_set_shard_waits_for_refhers_to_finish(
    db_session: Session,
    repo: Repo,
    workspace_api: WorkspaceAPI,
    dataset_api: DatasetAPI,
    permission_set_factory,
    capacity_factory,
    model_container,
    create_not_existing_workspace_permission_set_1,
    asign_temporary_capacity_to_permission_set_1_workspace,
    prepare_model_on_blob,
    deploy_permission_set_1_dataset_from_blob,
    get_uploaded_permission_set_1_dataset_id_with_retry,
    take_ownership_to_permission_set_1_dataset,
    update_permission_in_permission_set_1_dataset,
    update_adls_url_in_permission_set_1_dataset,
    verify_permission_set_1_in_dataset,
    verify_adls_url_1_in_dataset,
    set_credentials_for_permission_set_1_datasource,
    refresh_permission_set_1_workspace_dataset,
    permission_set_1_dataset_refresh_is_pending,
    permission_set_1_dataset_refresh_has_finished,
    mock_sleep_on_shard_logic: MagicMock,
):
    permission_set = permission_set_factory()
    capacity_factory(
        id="capacity-temporary-c13d-451b-af5f-ed0c46",
        name="temporary_capacity_name",
        is_default_for_releases=True,
    )

    create_permission_set_shard(
        repo=repo,
        workspace_api=workspace_api,
        dataset_api=dataset_api,
        model_container=model_container,
        request=CreatePermissionSetShardRequest(
            permission_set_uuid=permission_set.uuid,
            shard_version=ShardVersion("2.5.0"),
            environment=Environment("local"),
            processed_adls_url=ProcessedAdlsURL("https://test.adls-url.not-existing"),
            processed_adls_credentials=ProcessedAdlsCredentials(
                "processed_adls_credential"
            ),
        ),
    )
    mock_sleep_on_shard_logic.assert_called_once_with(30)


def test_create_new_permission_set_shard_triggers_new_refresh_is_missing_for_the_first_time_waits_5s(
    repo: Repo,
    workspace_api: WorkspaceAPI,
    dataset_api: DatasetAPI,
    permission_set_factory,
    capacity_factory,
    model_container,
    create_not_existing_workspace_permission_set_1,
    asign_temporary_capacity_to_permission_set_1_workspace,
    prepare_model_on_blob,
    deploy_permission_set_1_dataset_from_blob,
    get_uploaded_permission_set_1_dataset_id_with_retry,
    take_ownership_to_permission_set_1_dataset,
    update_permission_in_permission_set_1_dataset,
    update_adls_url_in_permission_set_1_dataset,
    verify_permission_set_1_in_dataset,
    verify_adls_url_1_in_dataset,
    set_credentials_for_permission_set_1_datasource,
    refresh_permission_set_1_workspace_dataset,
    permission_set_1_dataset_refresh_is_missing,
    permission_set_1_dataset_refresh_has_finished,
    mock_sleep_on_shard_logic: MagicMock,
    responses_mock: responses.RequestsMock,
):
    permission_set = permission_set_factory()
    capacity_factory(
        id="capacity-temporary-c13d-451b-af5f-ed0c46",
        name="temporary_capacity_name",
        is_default_for_releases=True,
    )

    create_permission_set_shard(
        repo=repo,
        workspace_api=workspace_api,
        dataset_api=dataset_api,
        model_container=model_container,
        request=CreatePermissionSetShardRequest(
            permission_set_uuid=permission_set.uuid,
            shard_version=ShardVersion("2.5.0"),
            environment=Environment("local"),
            processed_adls_url=ProcessedAdlsURL("https://test.adls-url.not-existing"),
            processed_adls_credentials=ProcessedAdlsCredentials(
                "processed_adls_credential"
            ),
        ),
    )

    mock_sleep_on_shard_logic.assert_called_once_with(5)
    responses_mock.assert_call_count(refresh_permission_set_1_workspace_dataset, 1)


def test_create_new_permission_set_shard_triggers_new_refresh_is_missing_for_the_second_time(
    repo: Repo,
    workspace_api: WorkspaceAPI,
    dataset_api: DatasetAPI,
    permission_set_factory,
    capacity_factory,
    model_container,
    create_not_existing_workspace_permission_set_1,
    asign_temporary_capacity_to_permission_set_1_workspace,
    prepare_model_on_blob,
    deploy_permission_set_1_dataset_from_blob,
    get_uploaded_permission_set_1_dataset_id_with_retry,
    take_ownership_to_permission_set_1_dataset,
    update_permission_in_permission_set_1_dataset,
    update_adls_url_in_permission_set_1_dataset,
    verify_permission_set_1_in_dataset,
    verify_adls_url_1_in_dataset,
    set_credentials_for_permission_set_1_datasource,
    refresh_permission_set_1_workspace_dataset,
    permission_set_1_dataset_refresh_is_missing_two_times,
    permission_set_1_dataset_refresh_has_finished,
    mock_sleep_on_shard_logic: MagicMock,
    responses_mock: responses.RequestsMock,
):
    permission_set = permission_set_factory()
    capacity_factory(
        id="capacity-temporary-c13d-451b-af5f-ed0c46",
        name="temporary_capacity_name",
        is_default_for_releases=True,
    )

    create_permission_set_shard(
        repo=repo,
        workspace_api=workspace_api,
        dataset_api=dataset_api,
        model_container=model_container,
        request=CreatePermissionSetShardRequest(
            permission_set_uuid=permission_set.uuid,
            shard_version=ShardVersion("2.5.0"),
            environment=Environment("local"),
            processed_adls_url=ProcessedAdlsURL("https://test.adls-url.not-existing"),
            processed_adls_credentials=ProcessedAdlsCredentials(
                "processed_adls_credential"
            ),
        ),
    )
    responses_mock.assert_call_count(refresh_permission_set_1_workspace_dataset, 2)


def test_create_new_permission_set_shard_triggers_new_refresh_and_raises_for_failed_refresh(
    repo: Repo,
    workspace_api: WorkspaceAPI,
    dataset_api: DatasetAPI,
    permission_set_factory,
    capacity_factory,
    model_container,
    create_not_existing_workspace_permission_set_1,
    asign_temporary_capacity_to_permission_set_1_workspace,
    prepare_model_on_blob,
    deploy_permission_set_1_dataset_from_blob,
    get_uploaded_permission_set_1_dataset_id_with_retry,
    take_ownership_to_permission_set_1_dataset,
    update_permission_in_permission_set_1_dataset,
    update_adls_url_in_permission_set_1_dataset,
    verify_permission_set_1_in_dataset,
    verify_adls_url_1_in_dataset,
    set_credentials_for_permission_set_1_datasource,
    refresh_permission_set_1_workspace_dataset,
    permission_set_1_dataset_refresh_is_failed,
    responses_mock: responses.RequestsMock,
):
    permission_set = permission_set_factory()
    capacity_factory(
        id="capacity-temporary-c13d-451b-af5f-ed0c46",
        name="temporary_capacity_name",
        is_default_for_releases=True,
    )
    with pytest.raises(RefreshFailed):
        create_permission_set_shard(
            repo=repo,
            workspace_api=workspace_api,
            dataset_api=dataset_api,
            model_container=model_container,
            request=CreatePermissionSetShardRequest(
                permission_set_uuid=permission_set.uuid,
                shard_version=ShardVersion("2.5.0"),
                environment=Environment("local"),
                processed_adls_url=ProcessedAdlsURL(
                    "https://test.adls-url.not-existing"
                ),
                processed_adls_credentials=ProcessedAdlsCredentials(
                    "processed_adls_credential"
                ),
            ),
        )

    responses_mock.assert_call_count(refresh_permission_set_1_workspace_dataset, 2)


def test_override_existing_workspaces_removes_old_dataset_if_exists(
    repo: Repo,
    workspace_api: WorkspaceAPI,
    dataset_api: DatasetAPI,
    permission_set_factory,
    capacity_factory,
    model_container,
    create_not_existing_workspace_permission_set_1,
    asign_temporary_capacity_to_permission_set_1_workspace,
    prepare_model_on_blob,
    deploy_permission_set_1_dataset_from_blob,
    get_uploaded_permission_set_1_dataset_id_with_retry,
    take_ownership_to_permission_set_1_dataset,
    update_permission_in_permission_set_1_dataset,
    update_adls_url_in_permission_set_1_dataset,
    verify_permission_set_1_in_dataset,
    verify_adls_url_1_in_dataset,
    set_credentials_for_permission_set_1_datasource,
    refresh_permission_set_1_workspace_dataset,
    permission_set_1_dataset_refresh_is_missing,
    permission_set_1_dataset_refresh_has_finished,
    mock_sleep_on_shard_logic: MagicMock,
    responses_mock: responses.RequestsMock,
):
    permission_set = permission_set_factory()
    capacity_factory(
        id="capacity-temporary-c13d-451b-af5f-ed0c46",
        name="temporary_capacity_name",
        is_default_for_releases=True,
    )

    create_permission_set_shard(
        repo=repo,
        workspace_api=workspace_api,
        dataset_api=dataset_api,
        model_container=model_container,
        request=CreatePermissionSetShardRequest(
            permission_set_uuid=permission_set.uuid,
            shard_version=ShardVersion("2.5.0"),
            environment=Environment("local"),
            processed_adls_url=ProcessedAdlsURL("https://test.adls-url.not-existing"),
            processed_adls_credentials=ProcessedAdlsCredentials(
                "processed_adls_credential"
            ),
        ),
    )


def test_raise_refresh_too_heavy_exception_if_refreshing_too_long(
    repo: Repo,
    workspace_api: WorkspaceAPI,
    dataset_api: DatasetAPI,
    permission_set_factory,
    capacity_factory,
    model_container,
    create_not_existing_workspace_permission_set_1,
    asign_temporary_capacity_to_permission_set_1_workspace,
    prepare_model_on_blob,
    deploy_permission_set_1_dataset_from_blob,
    get_uploaded_permission_set_1_dataset_id_with_retry,
    take_ownership_to_permission_set_1_dataset,
    update_permission_in_permission_set_1_dataset,
    update_adls_url_in_permission_set_1_dataset,
    verify_permission_set_1_in_dataset,
    verify_adls_url_1_in_dataset,
    set_credentials_for_permission_set_1_datasource,
    refresh_permission_set_1_workspace_dataset,
    permission_set_1_dataset_refresh_is_pending,
    mock_sleep_on_shard_logic: MagicMock,
):
    permission_set = permission_set_factory()
    capacity_factory(
        id="capacity-temporary-c13d-451b-af5f-ed0c46",
        name="temporary_capacity_name",
        is_default_for_releases=True,
    )

    with pytest.raises(RefreshTooHeavyPermissionSet):
        create_permission_set_shard(
            repo=repo,
            workspace_api=workspace_api,
            dataset_api=dataset_api,
            model_container=model_container,
            request=CreatePermissionSetShardRequest(
                permission_set_uuid=permission_set.uuid,
                shard_version=ShardVersion("2.5.0"),
                environment=Environment("local"),
                processed_adls_url=ProcessedAdlsURL(
                    "https://test.adls-url.not-existing"
                ),
                processed_adls_credentials=ProcessedAdlsCredentials(
                    "processed_adls_credential"
                ),
            ),
        )

    assert mock_sleep_on_shard_logic.call_count == 240
