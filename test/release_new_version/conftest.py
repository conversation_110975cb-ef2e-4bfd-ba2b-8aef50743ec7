from unittest.mock import patch

import pytest
import responses
from freezegun import freeze_time
from responses import matchers


@pytest.fixture
def create_permission_set_shard_mock():
    with patch("dataset_manager.logic.release.create_permission_set_shard") as mock:
        yield mock


@pytest.fixture
def asign_to_profile_mock():
    with patch("dataset_manager.logic.release.asign_to_profile") as mock:
        yield mock


@pytest.fixture
def assing_to_best_capacity_mock():
    with patch("dataset_manager.logic.release.assing_to_best_capacity") as mock:
        yield mock


@pytest.fixture
def get_profiles_another_existing(responses_mock: responses.RequestsMock):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/profiles",
        json={
            "value": [
                {
                    "id": "3b211778-e7a5-4d73-8187-f10824047724",
                    "displayName": "Some existing profile",
                }
            ]
        },
    )


@pytest.fixture
def create_profile_for_studio_1(responses_mock: responses.RequestsMock):
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/profiles",
        json={
            "id": "profile1-54b7-43f4-b072-ed4c1f9d5824",
            "displayName": "local_1",
        },
        match=[matchers.json_params_matcher({"displayName": "local_1"})],
    )


@pytest.fixture
def create_not_existing_workspace_permission_set_1(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups",
        json={"value": []},
        match=[
            matchers.query_param_matcher(
                {
                    "$filter": "name eq 'local_2.5.0_eeeeeee1-7d79-4403-83e9-916b65129739'"
                }
            )
        ],
    )
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups",
        json={
            "id": "workspace1-ps1-4e18-aea3-4cb4a3a50b48",
            "isReadOnly": False,
            "isOnDedicatedCapacity": False,
            "name": "local_2.5.0_eeeeeee1-7d79-4403-83e9-916b65129739",
        },
        match=[
            matchers.json_params_matcher(
                {"name": "local_2.5.0_eeeeeee1-7d79-4403-83e9-916b65129739"}
            )
        ],
    )


@pytest.fixture
def prepare_model_on_blob(models_client):
    models_client.upload_blob(name="2.5.0", data=b"IndieBI Dataset From Blob")


@pytest.fixture
def asign_studio_1_profile_to_permission_set_1_shard_workspace(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/users",
        json={"value": []},
    )
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/users",
        match=[
            matchers.json_params_matcher(
                {
                    "groupUserAccessRight": "Admin",
                    "displayName": "local_1",
                    "identifier": "mainprincipal-123-123-123-33333333",
                    "principalType": "App",
                    "profile": {"id": "profile1-54b7-43f4-b072-ed4c1f9d5824"},
                }
            )
        ],
    )


@pytest.fixture
def resume_temoprary_capacity(
    responses_mock: responses.RequestsMock,
):
    responses_mock.post(
        "https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/temporary_capacity_name/resume?api-version=2021-01-01",
    )


@pytest.fixture
def active_temporary_capacity(capacity_factory):
    capacity_factory(
        id="capacity-temporary-c13d-451b-af5f-ed0c46",
        name="temporary_capacity_name",
        is_default_for_releases=True,
    )


@pytest.fixture
def get_details_of_temporary_capacity_to_be_resumed(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/capacities",
        json={
            "value": [
                {
                    "id": "capacity-temporary-c13d-451b-af5f-ed0c46",
                    "displayName": "temporary_capacity_name",
                    "admins": ["<EMAIL>"],
                    "sku": "A2",
                    "state": "Paused",
                    "capacityUserAccessRight": "Admin",
                    "region": "West Europe",
                    "users": [],
                }
            ],
        },
    )
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/capacities",
        json={
            "value": [
                {
                    "id": "capacity-temporary-c13d-451b-af5f-ed0c46",
                    "displayName": "temporary_capacity_name",
                    "admins": ["<EMAIL>"],
                    "sku": "A2",
                    "state": "Active",
                    "capacityUserAccessRight": "Admin",
                    "region": "West Europe",
                    "users": [],
                }
            ],
        },
    )


@pytest.fixture
def pause_temporary_capacity(
    responses_mock: responses.RequestsMock,
):
    responses_mock.post(
        "https://management.azure.com/subscriptions/97bd5d65-c018-4632-b6c3-0feeb064b3d1/resourceGroups/indiebi-dev/providers/Microsoft.PowerBIDedicated/capacities/temporary_capacity_name/suspend?api-version=2021-01-01",
    )


@pytest.fixture
def get_details_of_temporary_capacity_to_be_paused(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/capacities",
        json={
            "value": [
                {
                    "id": "capacity-temporary-c13d-451b-af5f-ed0c46",
                    "displayName": "temporary_capacity_name",
                    "admins": ["<EMAIL>"],
                    "sku": "A2",
                    "state": "Active",
                    "capacityUserAccessRight": "Admin",
                    "region": "West Europe",
                    "users": [],
                }
            ],
        },
    )
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/capacities",
        json={
            "value": [
                {
                    "id": "capacity-temporary-c13d-451b-af5f-ed0c46",
                    "displayName": "temporary_capacity_name",
                    "admins": ["<EMAIL>"],
                    "sku": "A2",
                    "state": "Paused",
                    "capacityUserAccessRight": "Admin",
                    "region": "West Europe",
                    "users": [],
                }
            ],
        },
    )


@pytest.fixture
def asign_temporary_capacity_to_permission_set_1_workspace(
    responses_mock: responses.RequestsMock,
):
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/AssignToCapacity",
        match=[
            matchers.json_params_matcher(
                {"capacityId": "capacity-temporary-c13d-451b-af5f-ed0c46"}
            )
        ],
    )


@pytest.fixture
def asign_random_capacity_to_permission_set_1_workspace(
    responses_mock: responses.RequestsMock,
):
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/AssignToCapacity",
        match=[
            matchers.json_params_matcher(
                {"capacityId": "capacity-nodeff2-c13d-451b-af5f-ed0c4664"}
            )
        ],
    )


@pytest.fixture
def deploy_permission_set_1_dataset_from_blob(responses_mock: responses.RequestsMock):
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/imports?datasetDisplayName=Dataset1500000000&nameConflict=CreateOrOverwrite",
        match=[matchers.multipart_matcher({"value": b"IndieBI Dataset From Blob"})],
        json={"id": "dataset-deploy-ps1-4d83-ae5a-014028cb0665"},
    )
    with freeze_time("2017-07-14 02:40"):
        yield


@pytest.fixture
def get_uploaded_permission_set_1_dataset_id_with_retry(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/imports/dataset-deploy-ps1-4d83-ae5a-014028cb0665",
        json={
            "id": "dataset-deploy-ps1-4d83-ae5a-014028cb0665",
            "importState": "Publishing",
            "createdDateTime": "2018-05-08T14:56:18.477Z",
            "updatedDateTime": "2018-05-08T14:56:18.477Z",
            "name": "v2",
            "connectionType": "import",
            "source": "Upload",
            "datasets": [],
            "reports": [],
        },
    )
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/imports/dataset-deploy-ps1-4d83-ae5a-014028cb0665",
        json={
            "id": "dataset-deploy-ps1-4d83-ae5a-014028cb0665",
            "importState": "Succeeded",
            "createdDateTime": "2018-05-08T14:56:18.477Z",
            "updatedDateTime": "2018-05-08T14:56:18.477Z",
            "name": "v2",
            "connectionType": "import",
            "source": "Upload",
            "datasets": [
                {
                    "id": "dataset_permission_set_1-8037-a46fb27",
                    "name": "v2",
                    "webUrl": "https://app.powerbi.com/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/datasets/dataset_permission_set_1-8037-a46fb27",
                }
            ],
            "reports": [],
        },
    )


@pytest.fixture
def take_ownership_to_permission_set_1_dataset(responses_mock: responses.RequestsMock):
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/datasets/dataset_permission_set_1-8037-a46fb27/Default.TakeOver"
    )


@pytest.fixture
def update_permission_in_permission_set_1_dataset(
    responses_mock: responses.RequestsMock,
):
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/datasets/dataset_permission_set_1-8037-a46fb27/Default.UpdateParameters",
        match=[
            matchers.json_params_matcher(
                {
                    "updateDetails": [
                        {
                            "name": "permission_set",
                            "newValue": '[{"studio_id": 1, "product_name": null}]',
                        },
                    ]
                }
            )
        ],
    )


@pytest.fixture
def update_adls_url_in_permission_set_1_dataset(
    responses_mock: responses.RequestsMock,
):
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/datasets/dataset_permission_set_1-8037-a46fb27/Default.UpdateParameters",
        match=[
            matchers.json_params_matcher(
                {
                    "updateDetails": [
                        {
                            "name": "URL_ROOT",
                            "newValue": "https://test.adls-url.not-existing",
                        },
                    ]
                }
            )
        ],
    )


@pytest.fixture
def verify_permission_set_1_in_dataset(responses_mock: responses.RequestsMock):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/datasets/dataset_permission_set_1-8037-a46fb27/parameters",
        json={
            "value": [
                {
                    "name": "RandomParamToIgnore",
                    "type": "Text",
                    "isRequired": True,
                    "currentValue": "IGNORE ME",
                },
                {
                    "name": "permission_set",
                    "type": "Text",
                    "isRequired": True,
                    "currentValue": '[{"studio_id": 1, "product_name": null}]',
                },
            ]
        },
    )


@pytest.fixture
def verify_adls_url_1_in_dataset(responses_mock: responses.RequestsMock):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/datasets/dataset_permission_set_1-8037-a46fb27/parameters",
        json={
            "value": [
                {
                    "name": "RandomParamToIgnore",
                    "type": "Text",
                    "isRequired": True,
                    "currentValue": "IGNORE ME",
                },
                {
                    "name": "URL_ROOT",
                    "type": "Text",
                    "isRequired": True,
                    "currentValue": "https://test.adls-url.not-existing",
                },
            ]
        },
    )


@pytest.fixture
def set_credentials_for_permission_set_1_datasource(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/datasets/dataset_permission_set_1-8037-a46fb27/datasources",
        json={
            "value": [
                {
                    "datasourceType": "AzureBlobs",
                    "datasourceId": "datasource_ps1-90b6-4fc0-abf2-2d60d448cb04",
                    "gatewayId": "gateway_ps1-544b-403f-b132-da0d3a024674",
                    "connectionDetails": {
                        "account": "myAccount",
                        "domain": "blob.core.windows.net",
                    },
                }
            ]
        },
    )
    responses_mock.patch(
        "https://api.powerbi.com/v1.0/myorg/gateways/gateway_ps1-544b-403f-b132-da0d3a024674/datasources/datasource_ps1-90b6-4fc0-abf2-2d60d448cb04",
        match=[
            matchers.json_params_matcher(
                {
                    "credentialDetails": {
                        "credentialType": "Key",
                        "credentials": '{"credentialData":[{"name":"key", "value":"processed_adls_credential"}]}',
                        "encryptedConnection": "Encrypted",
                        "encryptionAlgorithm": "None",
                        "privacyLevel": "Organizational",
                    }
                }
            )
        ],
    )


@pytest.fixture
def refresh_permission_set_1_workspace_dataset(responses_mock: responses.RequestsMock):
    url = "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/datasets/dataset_permission_set_1-8037-a46fb27/refreshes"
    responses_mock.post(
        url,
        match=[
            matchers.json_params_matcher(
                {"refresh": {"objects": [{"database": "Dataset"}], "type": "full"}}
            ),
        ],
    )
    return url


@pytest.fixture
def permission_set_1_dataset_refresh_has_finished(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/datasets/dataset_permission_set_1-8037-a46fb27/refreshes",
        match=[matchers.query_param_matcher({"$top": "1"})],
        json={
            "value": [
                {
                    "refreshType": "ViaApi",
                    "startTime": "2017-06-13T09:25:43.153Z",
                    "endTime": "2017-06-13T09:31:43.153Z",
                    "status": "Completed",
                    "requestId": "refresh_id-25d1-44f8-8576-136d7e9014b1",
                }
            ]
        },
    )


@pytest.fixture
def permission_set_1_dataset_refresh_is_pending(responses_mock: responses.RequestsMock):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/datasets/dataset_permission_set_1-8037-a46fb27/refreshes",
        match=[matchers.query_param_matcher({"$top": "1"})],
        json={
            "value": [
                {
                    "refreshType": "ViaApi",
                    "startTime": "2017-06-13T09:25:43.153Z",
                    "endTime": "2017-06-13T09:31:43.153Z",
                    "status": "Unknown",
                    "requestId": "refresh_id-25d1-44f8-8576-136d7e9014b1",
                }
            ]
        },
    )


@pytest.fixture
def permission_set_1_dataset_refresh_is_failed(responses_mock: responses.RequestsMock):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/datasets/dataset_permission_set_1-8037-a46fb27/refreshes",
        match=[matchers.query_param_matcher({"$top": "1"})],
        json={
            "value": [
                {
                    "refreshType": "ViaApi",
                    "startTime": "2017-06-13T09:25:43.153Z",
                    "endTime": "2017-06-13T09:31:43.153Z",
                    "status": "Failed",
                    "requestId": "refresh_id-25d1-44f8-8576-136d7e9014b1",
                }
            ]
        },
    )


@pytest.fixture
def permission_set_1_dataset_refresh_is_missing(responses_mock: responses.RequestsMock):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/datasets/dataset_permission_set_1-8037-a46fb27/refreshes",
        match=[matchers.query_param_matcher({"$top": "1"})],
        json={"value": []},
    )


@pytest.fixture
def permission_set_1_dataset_refresh_is_missing_two_times(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/datasets/dataset_permission_set_1-8037-a46fb27/refreshes",
        match=[matchers.query_param_matcher({"$top": "1"})],
        json={"value": []},
    )
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/workspace1-ps1-4e18-aea3-4cb4a3a50b48/datasets/dataset_permission_set_1-8037-a46fb27/refreshes",
        match=[matchers.query_param_matcher({"$top": "1"})],
        json={"value": []},
    )


@pytest.fixture
def asign_studio_1_profile_to_version_workspaces(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/main-workspace-xyz-xyz-12312312312/users",
        json={"value": []},
    )
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups/main-workspace-xyz-xyz-12312312312/users",
        match=[
            matchers.json_params_matcher(
                {
                    "groupUserAccessRight": "Admin",
                    "displayName": "local_1",
                    "identifier": "mainprincipal-123-123-123-33333333",
                    "principalType": "App",
                    "profile": {"id": "profile1-54b7-43f4-b072-ed4c1f9d5824"},
                }
            )
        ],
    )


@pytest.fixture
def skip_assign_studio_1_profile_to_shard_template_workspaces_because_profile_already_exists(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/shard-template-workspace-bf7d-13880a5c3c/users",
        json={
            "value": [
                {
                    "groupUserAccessRight": "Admin",
                    "displayName": "local_1",
                    "identifier": "mainprincipal-123-123-123-33333333",
                    "principalType": "App",
                    "profile": {"id": "profile1-54b7-43f4-b072-ed4c1f9d5824"},
                }
            ]
        },
    )
