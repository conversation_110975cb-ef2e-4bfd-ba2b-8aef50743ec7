from sqlalchemy.orm import Session

from dataset_manager.azure.powerbi.workspaces import WorkspaceAPI
from dataset_manager.entities import (
    Environment,
    PermissionSetUUID,
    ServicePrincipalId,
    StudioId,
    VersionId,
    WorkspaceId,
)
from dataset_manager.logic.shard import AsignToProfileRequest, asign_to_profile
from dataset_manager.repo import Repo
from dataset_manager.repo.profile import _DB<PERSON>ro<PERSON>le


def test_asign_to_new_profile(
    db_session: Session,
    repo: Repo,
    workspace_api: WorkspaceAPI,
    power_bi_api,
    profile_factory,
    get_profiles_another_existing,
    create_profile_for_studio_1,
    asign_studio_1_profile_to_permission_set_1_shard_workspace,
    asign_studio_1_profile_to_version_workspaces,
    skip_assign_studio_1_profile_to_shard_template_workspaces_because_profile_already_exists,
):
    asign_to_profile(
        repo=repo,
        workspace_api=workspace_api,
        request=AsignToProfileRequest(
            workspaces=[
                WorkspaceId("workspace1-ps1-4e18-aea3-4cb4a3a50b48"),
                WorkspaceId("main-workspace-xyz-xyz-12312312312"),
                WorkspaceId("shard-template-workspace-bf7d-13880a5c3c"),
            ],
            environment=Environment("local"),
            studio_id=StudioId(1),
            version_id=VersionId("3.0.0"),
            permission_set_uuid=PermissionSetUUID(
                "eeeeeee1-7d79-4403-83e9-916b65129739",
            ),
            service_principal_id=ServicePrincipalId(
                "mainprincipal-123-123-123-33333333"
            ),
        ),
    )

    all_profiles_created = db_session.query(_DBProfile).all()
    profile = all_profiles_created[0]

    assert profile.studio_id == 1
    assert profile.profile_name == "local_1"
    assert profile.profile_id == "profile1-54b7-43f4-b072-ed4c1f9d5824"
    assert str(profile.active_version_id) == "3.0.0"
    assert profile.permission_set_uuid == "eeeeeee1-7d79-4403-83e9-916b65129739"

    assert len(all_profiles_created) == 1


def test_asign_to_existing_profile(
    db_session: Session,
    repo: Repo,
    workspace_api: WorkspaceAPI,
    profile_factory,
    asign_studio_1_profile_to_permission_set_1_shard_workspace,
    asign_studio_1_profile_to_version_workspaces,
    skip_assign_studio_1_profile_to_shard_template_workspaces_because_profile_already_exists,
):
    profile_factory(
        studio_id=1,
        active_version_id="2.0.0",
        profile_id="profile1-54b7-43f4-b072-ed4c1f9d5824",
        permission_set_uuid="eeeeeee1-7d79-4403-83e9-916b65129739",
    )
    asign_to_profile(
        repo=repo,
        workspace_api=workspace_api,
        request=AsignToProfileRequest(
            workspaces=[
                WorkspaceId("workspace1-ps1-4e18-aea3-4cb4a3a50b48"),
                WorkspaceId("main-workspace-xyz-xyz-12312312312"),
                WorkspaceId("shard-template-workspace-bf7d-13880a5c3c"),
            ],
            environment=Environment("local"),
            studio_id=StudioId(1),
            version_id=VersionId("3.0.0"),
            permission_set_uuid=PermissionSetUUID(
                "fffffff2-7d79-4403-83e9-916b65129739",
            ),
            service_principal_id=ServicePrincipalId(
                "mainprincipal-123-123-123-33333333"
            ),
        ),
    )

    released_studio_profile = (
        db_session.query(_DBProfile).filter(_DBProfile.studio_id == 1).one()
    )
    assert released_studio_profile.active_version_id == "3.0.0"
    assert (
        released_studio_profile.permission_set_uuid
        == "fffffff2-7d79-4403-83e9-916b65129739"
    )
