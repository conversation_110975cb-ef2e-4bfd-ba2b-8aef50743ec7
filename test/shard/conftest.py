import pytest
import responses
from responses import matchers

from dataset_manager.config import get_config
from dataset_manager.main import app


@pytest.fixture
def assign_capacity_to_chosen_one(responses_mock: responses.RequestsMock):
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups/22222222-1758-40f1-bf7d-13880a5c3ce5/AssignToCapacity",
        match=[matchers.json_params_matcher({"capacityId": "xxx-xxx-capacity"})],
    )


@pytest.fixture
def asign_debug_admins_to_shard(responses_mock: responses.RequestsMock):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/22222222-1758-40f1-bf7d-13880a5c3ce5/users",
        json={"value": []},
    )
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups/22222222-1758-40f1-bf7d-13880a5c3ce5/users",
        match=[
            matchers.json_params_matcher(
                {
                    "groupUserAccessRight": "Admin",
                    "displayName": "<PERSON>",
                    "identifier": "<EMAIL>",
                    "principalType": "User",
                    "emailAddress": "<EMAIL>",
                }
            )
        ],
    )


@pytest.fixture
def configure_debug_admins():
    class FakeConfig:
        DEBUG_ADMINS = [
            {
                "groupUserAccessRight": "Admin",
                "displayName": "John Doe",
                "identifier": "<EMAIL>",
                "principalType": "User",
                "emailAddress": "<EMAIL>",
            }
        ]

    config = FakeConfig()

    def override_config():
        yield config

    app.dependency_overrides[get_config] = override_config
    yield config
    app.dependency_overrides = {}


@pytest.fixture
def dataset_refresh_is_finished(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/22222222-1758-40f1-bf7d-13880a5c3ce5/datasets/11111111-1758-40f1-bf7d-13880a5c3ce5/refreshes",
        match=[matchers.query_param_matcher({"$top": "1"})],
        json={
            "value": [
                {
                    "refreshType": "ViaApi",
                    "startTime": "2017-06-13T09:25:43.153Z",
                    "endTime": "2017-06-13T09:31:43.153Z",
                    "status": "Completed",
                    "requestId": "refresh_id-25d1-44f8-8576-136d7e9014b1",
                }
            ]
        },
    )


@pytest.fixture
def dataset_refresh_is_pending(responses_mock: responses.RequestsMock):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/22222222-1758-40f1-bf7d-13880a5c3ce5/datasets/11111111-1758-40f1-bf7d-13880a5c3ce5/refreshes",
        match=[matchers.query_param_matcher({"$top": "1"})],
        json={
            "value": [
                {
                    "refreshType": "ViaApi",
                    "startTime": "2017-06-13T09:25:43.153Z",
                    "status": "Unknown",
                    "requestId": "refresh_id-25d1-44f8-8576-136d7e9014b1",
                }
            ]
        },
    )


@pytest.fixture
def dataset_refresh_is_failed(responses_mock: responses.RequestsMock):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/22222222-1758-40f1-bf7d-13880a5c3ce5/datasets/11111111-1758-40f1-bf7d-13880a5c3ce5/refreshes",
        match=[matchers.query_param_matcher({"$top": "1"})],
        json={
            "value": [
                {
                    "refreshType": "ViaApi",
                    "startTime": "2017-06-13T09:25:43.153Z",
                    "status": "Failed",
                    "requestId": "refresh_id-25d1-44f8-8576-136d7e9014b1",
                    "endTime": "2024-02-06T08:45:48.297Z",
                    "serviceExceptionJson": '{"errorCode":"ModelRefresh_ShortMessage_ProcessingError","errorDescription":"Resource Governing: This operation was canceled because ..."}',
                }
            ]
        },
    )


@pytest.fixture
def incremental_refresh_workspace_dataset(responses_mock: responses.RequestsMock):
    url = "https://api.powerbi.com/v1.0/myorg/groups/22222222-1758-40f1-bf7d-13880a5c3ce5/datasets/11111111-1758-40f1-bf7d-13880a5c3ce5/refreshes"
    responses_mock.post(
        url,
        match=[
            matchers.json_params_matcher(
                {
                    "type": "full",
                    "applyRefreshPolicy": True,
                }
            ),
        ],
    )


@pytest.fixture
def refresh_workspace_dataset(responses_mock: responses.RequestsMock):
    url = "https://api.powerbi.com/v1.0/myorg/groups/22222222-1758-40f1-bf7d-13880a5c3ce5/datasets/11111111-1758-40f1-bf7d-13880a5c3ce5/refreshes"
    responses_mock.post(
        url,
        match=[
            matchers.json_params_matcher(
                {
                    "type": "full",
                    "applyRefreshPolicy": False,
                }
            ),
        ],
    )


@pytest.fixture
def workspace_dataset_is_already_being_refreshed(
    responses_mock: responses.RequestsMock,
):
    url = "https://api.powerbi.com/v1.0/myorg/groups/22222222-1758-40f1-bf7d-13880a5c3ce5/datasets/11111111-1758-40f1-bf7d-13880a5c3ce5/refreshes"
    responses_mock.post(
        url,
        match=[
            matchers.json_params_matcher(
                {
                    "type": "full",
                    "applyRefreshPolicy": False,
                }
            ),
        ],
        status=400,
        json={
            "error": {
                "code": "InvalidRequest",
                "message": "Invalid dataset refresh request. Another refresh request is already executing",
            }
        },
    )
