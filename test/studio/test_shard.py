from sqlalchemy.orm import Session
from starlette.status import HTTP_200_OK, HTTP_403_FORBIDDEN, HTTP_404_NOT_FOUND


def test_API_call_without_auth_header_fails(db_session: Session, client):
    response = client.get("/studio/1/shard/active")
    assert response.status_code == HTTP_403_FORBIDDEN


def test_not_existing_customer_returns_404(
    db_session: Session, client, authorization_header
):
    response = client.get(
        "/studio/1/shard/active",
        headers=authorization_header,
    )
    assert response.status_code == HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Studio not found"}


def test_existing_cusomer_returns_200(
    db_session: Session, client, authorization_header, existing_customer
):
    response = client.get(
        "/studio/1/shard/active",
        headers=authorization_header,
    )
    assert response.status_code == HTTP_200_OK


def test_existing_cusomer_returns_active_shard(
    db_session: Session, client, authorization_header, existing_customer
):
    response = client.get(
        "/studio/1/shard/active",
        headers=authorization_header,
    )

    shard = response.json()
    assert shard == {
        "version": "v1",
        "dataset_id": "11111111-1758-40f1-bf7d-13880a5c3ce5",
        "dataset_name": None,
        "workspace_id": "*************-40f1-bf7d-13880a5c3ce5",
        "workspace_name": "sample_workspace_name",
        "capacity_id": "*************-40f1-bf7d-13880a5c3ce5",
        "creation_timestamp": "2022-08-08T10:00:00",
        "permission_set_uuid": "eeeeeee1-7d79-4403-83e9-916b65129739",
        "last_refresh_timestamp": "2022-08-08T10:10:10",
    }
