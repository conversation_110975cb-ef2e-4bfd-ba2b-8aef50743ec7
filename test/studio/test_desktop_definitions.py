import pytest
from sqlalchemy.orm import Session
from starlette.status import HTTP_200_OK, HTTP_403_FORBIDDEN, HTTP_404_NOT_FOUND


@pytest.fixture
def create_non_default_version(version_factory):
    version_factory(
        id="non_default_version",
        visuals_desktop_definitions={
            "version": "non_default_version",
            "last_updated": "2022-11-11T11:11:11.111111",
            "menu_items": [
                {
                    "id": "c069714b-0fdb-416d-a145-ef473be8c9c2",
                    "type": "powerbi",
                    "icon": "trophy-outline",
                    "display_name": "Top products",
                    "internal_name": "Top Products",
                    "pbix_name": "Top Products",
                    "page_name": "ReportSection015a5759087d2bcc815b",
                    "visual_container_id": "191997966853de58c006",
                    "description": {
                        "title": "Your top products",
                        "description": "A simple, summarized view of your best selling products.",
                        "features": [
                            "See your top 10 products in a given country and region",
                            "Check the percentage of your best products in total sales",
                        ],
                    },
                    "dashboard_url": "https://app.powerbi.com/reportEmbed?reportId=7350235f-5c22-4bec-9bbf-0c3b7d99dbb8&groupId=07d60db4-cc0e-4ca0-9f3e-64e69b025d3b&language=en&formatLocale=en-US",
                }
            ],
        },
        is_default=False,
    )


def test_API_call_without_auth_header_fails(db_session: Session, client):
    response = client.get("/studio/1/desktop-definitions")
    assert response.status_code == HTTP_403_FORBIDDEN


def test_not_existing_customer_returns_404(
    db_session: Session, client, authorization_header
):
    response = client.get(
        "/studio/1/desktop-definitions",
        headers=authorization_header,
    )
    assert response.status_code == HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Studio not found"}


def test_existing_cusomer_returns_200(
    db_session: Session, client, authorization_header, existing_customer
):
    response = client.get(
        "/studio/1/desktop-definitions",
        headers=authorization_header,
    )
    assert response.status_code == HTTP_200_OK


def test_existing_cusomer_returns_active_version_desktop_definitions_for_dev_env(
    db_session: Session, client, authorization_header, existing_customer
):
    response = client.get(
        "/studio/1/desktop-definitions",
        headers=authorization_header,
    )

    desktop_definition = response.json()
    assert desktop_definition["version"] == "2.0.4"
    assert desktop_definition["last_updated"] == "2022-09-07T11:51:06.042931"


def test_existing_cusomer_returns_active_version_desktop_definitions_for_prod_env(
    db_session: Session, client, authorization_header, existing_customer
):
    response = client.get(
        "/studio/1/desktop-definitions",
        headers=authorization_header,
    )

    desktop_definition = response.json()
    assert desktop_definition["version"] == "2.0.4"
    assert desktop_definition["last_updated"] == "2022-09-07T11:51:06.042931"


def test_existing_customer_returns_404_for_not_existing_version_requested(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
):
    response = client.get(
        "/studio/1/desktop-definitions/version/not_existing",
        headers=authorization_header,
    )
    desktop_definition = response.json()

    assert response.status_code == HTTP_404_NOT_FOUND
    assert desktop_definition == {"detail": "Version not found"}


def test_existing_customer_with_two_versions_returns_requested_one(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    create_non_default_version,
):
    response = client.get(
        "/studio/1/desktop-definitions/version/non_default_version",
        headers=authorization_header,
    )
    desktop_definition = response.json()

    assert response.status_code == HTTP_200_OK
    assert desktop_definition["version"] == "non_default_version"
    assert desktop_definition["last_updated"] == "2022-11-11T11:11:11.111111"
