import importlib
import json
import logging
import logging.config
import os

import pytest

from dataset_manager.logs import add_logging_context, configure_logger


@pytest.fixture(autouse=True)
def reset_logger():
    def _reset_logger():
        logging.shutdown()
        importlib.reload(logging)
        importlib.reload(logging.config)

    _reset_logger()
    yield
    _reset_logger()


def test_configured_logger_logs_to_stdout(caplog, capsys):
    configure_logger()

    logging.getLogger("test").info("Oh, hi Mark!")

    captured = capsys.readouterr()
    assert len(captured.out) != 0
    assert len(captured.err) == 0
    assert len(caplog.records) == 0


def test_configured_logger_logs_ecs_formatted_messages(capsys):
    configure_logger()
    test_msg = "Oh, hi Mark!"
    logging.getLogger("test").info(test_msg)
    captured = capsys.readouterr()
    assert captured.out
    message = json.loads(captured.out)
    assert message["@timestamp"] is not None
    assert message["log.level"] == "info"
    assert message["message"] == test_msg
    assert message["log"]["origin"]["file"]["name"] == os.path.basename(__file__)
    assert (
        message["log"]["origin"]["function"]
        == "test_configured_logger_logs_ecs_formatted_messages"
    )
    assert message["log"]["logger"] == "test"


def test_add_logging_context_logs_extra_under_provided_name(capsys):
    configure_logger()
    logger = logging.getLogger("test")
    with add_logging_context("test_context", extra_attribute="extra_value"):
        logger.info("Oh, hi Mark!")

    captured = capsys.readouterr()
    message = json.loads(captured.out)
    assert "test_context" in message
    assert message["test_context"] == {"extra_attribute": "extra_value"}
