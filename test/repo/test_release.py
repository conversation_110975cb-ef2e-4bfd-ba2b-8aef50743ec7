from dataset_manager.entities import ReleaseUUID
from dataset_manager.repo import Repo
from dataset_manager.repo.release import ReleaseStatus


def test_requested_assign_are_filtered_by_ignored_uuid(repo: Repo, release_factory):
    release_to_ignore = release_factory(status=ReleaseStatus.REQUESTED_ASSIGN)

    release_factory(status=ReleaseStatus.REQUESTED_ASSIGN)
    release_factory(status=ReleaseStatus.REQUESTED_ASSIGN)

    filtered_releases = repo.release.requested_assign(
        limit=100, ignore_uuids=[ReleaseUUID(release_to_ignore.uuid)]
    )

    assert len(filtered_releases) == 2


def test_requested_assign_are_filtered_other_statuses(repo: Repo, release_factory):
    release_factory(status=ReleaseStatus.REQUESTED_ASSIGN)

    release_factory(status=ReleaseStatus.REQUESTED)
    release_factory(status=ReleaseStatus.REQUESTED_SHARD)
    release_factory(status=ReleaseStatus.DONE)
    release_factory(status=ReleaseStatus.OUTDATED)
    release_factory(status=ReleaseStatus.FAILED)
    release_factory(status=ReleaseStatus.CANCELED)

    filtered_releases = repo.release.requested_assign(limit=100)

    assert len(filtered_releases) == 1
