from unittest import mock

import pytest
from azure.core.exceptions import AzureError
from fastapi import status
from sqlalchemy.exc import SQLAlchemyError

from dataset_manager.azure.exceptions import PBIError


@pytest.mark.usefixtures("db_session", "container_client", "azure_api_client")
@mock.patch("dataset_manager.api.healthcheck.request")
def test_synthetic_health_check_returns_good_status_when_services_are_connected(
    mock_request, authorization_header, client
):
    response = client.get("/health/synthetic", headers=authorization_header)

    assert response.status_code == status.HTTP_200_OK

    assert response.json()["database_connected"] is True
    assert response.json()["database_duration"] is not None
    assert response.json()["storage_connected"] is True
    assert response.json()["storage_duration"] is not None
    assert response.json()["pbi_api_available"] is True
    assert response.json()["pbi_api_duration"] is not None
    assert response.json()["azure_api_available"] is True
    assert response.json()["azure_api_duration"] is not None


@pytest.mark.usefixtures("db_session", "container_client", "azure_api_client")
@mock.patch("dataset_manager.api.healthcheck.select")
@mock.patch("dataset_manager.api.healthcheck.request")
def test_synthetic_health_check_returns_database_not_connected_on_db_error(
    mock_request, mock_select, authorization_header, client
):
    mock_select.side_effect = SQLAlchemyError()

    response = client.get("/health/synthetic", headers=authorization_header)

    assert response.status_code == status.HTTP_200_OK

    assert response.json()["database_connected"] is False
    assert response.json()["database_duration"] is None
    assert response.json()["storage_connected"] is True
    assert response.json()["storage_duration"] is not None
    assert response.json()["pbi_api_available"] is True
    assert response.json()["pbi_api_duration"] is not None
    assert response.json()["azure_api_available"] is True
    assert response.json()["azure_api_duration"] is not None


@pytest.mark.usefixtures("db_session", "azure_api_client")
@mock.patch("dataset_manager.api.healthcheck.request")
def test_synthetic_health_check_returns_storage_not_connected_when_container_does_not_exist(
    mock_request, authorization_header, client, container_client
):
    container_client.container_exists = False
    response = client.get("/health/synthetic", headers=authorization_header)

    assert response.status_code == status.HTTP_200_OK

    assert response.json()["database_connected"] is True
    assert response.json()["database_duration"] is not None
    assert response.json()["storage_connected"] is False
    assert response.json()["storage_duration"] is not None
    assert response.json()["pbi_api_available"] is True
    assert response.json()["pbi_api_duration"] is not None
    assert response.json()["azure_api_available"] is True
    assert response.json()["azure_api_duration"] is not None


@pytest.mark.usefixtures("db_session", "azure_api_client")
@mock.patch("dataset_manager.api.healthcheck.request")
def test_synthetic_health_check_returns_storage_not_connected_when_storage_returns_an_error(
    mock_request, authorization_header, client, container_client
):
    container_client.exception = AzureError("Error")
    response = client.get("/health/synthetic", headers=authorization_header)

    assert response.status_code == status.HTTP_200_OK

    assert response.json()["database_connected"] is True
    assert response.json()["database_duration"] is not None
    assert response.json()["storage_connected"] is False
    assert response.json()["storage_duration"] is None
    assert response.json()["pbi_api_available"] is True
    assert response.json()["pbi_api_duration"] is not None
    assert response.json()["azure_api_available"] is True
    assert response.json()["azure_api_duration"] is not None


@pytest.mark.usefixtures("db_session", "container_client", "azure_api_client")
@mock.patch("dataset_manager.api.healthcheck.request")
def test_synthetic_health_check_returns_pbi_api_not_available_when_pbi_api_does_not_respond(
    mock_request,
    authorization_header,
    client,
):
    mock_request.side_effect = Exception("Not available")
    response = client.get("/health/synthetic", headers=authorization_header)

    assert response.status_code == status.HTTP_200_OK

    assert response.json()["database_connected"] is True
    assert response.json()["database_duration"] is not None
    assert response.json()["storage_connected"] is True
    assert response.json()["storage_duration"] is not None
    assert response.json()["pbi_api_available"] is False
    assert response.json()["pbi_api_duration"] is None
    assert response.json()["azure_api_available"] is True
    assert response.json()["azure_api_duration"] is not None


@pytest.mark.usefixtures("db_session", "container_client")
@mock.patch("dataset_manager.api.healthcheck.request")
def test_synthetic_health_check_returns_azure_api_not_available_when_azure_api_does_not_respond(
    mock_request,
    azure_api_client,
    authorization_header,
    client,
):
    azure_api_client.exception = PBIError("Not available")
    response = client.get("/health/synthetic", headers=authorization_header)

    assert response.status_code == status.HTTP_200_OK

    assert response.json()["database_connected"] is True
    assert response.json()["database_duration"] is not None
    assert response.json()["storage_connected"] is True
    assert response.json()["storage_duration"] is not None
    assert response.json()["pbi_api_available"] is True
    assert response.json()["pbi_api_duration"] is not None
    assert response.json()["azure_api_available"] is False
    assert response.json()["azure_api_duration"] is None
