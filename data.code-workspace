{"folders": [{"name": "dataset-manager", "path": "."}, {"name": "PBI Embedded", "path": "../pbi-embedded"}, {"name": "Scraper API", "path": "../scraper-api"}, {"name": "Events Service", "path": "../events-service"}, {"name": "dataviz-dashboards", "path": "../dataviz-dashboards"}, {"name": "Single Click", "path": "../single-click-indiebi"}, {"name": "steam-app-scraper", "path": "../steam-app-scraper"}, {"name": "data-jobs", "path": "../data-jobs"}, {"name": "[LIBS] data_sdk", "path": "../data-jobs/libs/data-sdk"}, {"name": "[JOB] core_silver", "path": "../data-jobs/jobs/core_silver"}, {"name": "[JOB] find_shards", "path": "../data-jobs/jobs/find_shards"}, {"name": "[JOB] pbi_refresh", "path": "../data-jobs/jobs/pbi_refresh"}, {"name": "[JOB] report_processor", "path": "../data-jobs/jobs/report_processor"}, {"name": "[JOB] update_shared", "path": "../data-jobs/jobs/update_shared"}, {"name": "[JOB] hydrate_pbi_gold", "path": "../data-jobs/jobs/hydrate_pbi_gold"}, {"name": "[JOB] saas_gold", "path": "../data-jobs/jobs/saas_gold"}, {"name": "[JOB] ppt_gold", "path": "../data-jobs/jobs/ppt_gold"}, {"name": "[JOB] events_service_gold", "path": "../data-jobs/jobs/events_service_gold"}], "settings": {"files.exclude": {"**/__pycache__": true}, "[python]": {"editor.defaultFormatter": "charliermarsh.ruff", "editor.formatOnSave": true}}, "extensions": {"recommendations": ["ms-python.python", "ms-vscode-remote.remote-wsl", "ms-toolsai.jupyter", "nickmillerdev.pytest-fixtures", "charliermarsh.ruff"]}}