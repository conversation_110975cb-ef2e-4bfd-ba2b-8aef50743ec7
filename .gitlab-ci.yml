variables:
  POETRY_CACHE_DIR: $CI_PROJECT_DIR/.poetry-cache
  POETRY_VIRTUALENVS_IN_PROJECT: "true"

stages:
  - prepare
  - test-image
  - deploy

.auto-main-auto-mr: &auto-main-auto-mr
  - if: $CI_MERGE_REQUEST_ID || $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

.auto-main-manual-mr: &auto-main-manual-mr
  - if: $CI_MERGE_REQUEST_ID
    when: manual
    allow_failure: true
  - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

.manual-main: &manual-main
  - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    when: manual

integration-tests:
  stage: prepare
  image: registry.gitlab.com/bluebrick/indiebi/infra/python-ci-image:python-3.10
  services:
    - name: crindiebimain.azurecr.io/cpt/local-sql-server-2022:master
      alias: mssql_db
    - name: mcr.microsoft.com/azure-storage/azurite:3.32.0
      alias: azurite
  variables:
    SQLALCHEMY_DATABASE_URL: "mssql+pyodbc://indiebi:Password1!@mssql_db/Shards?driver=ODBC+Driver+17+for+SQL+Server"
    AZURITE_HOST: azurite
  script:
    - unset SENTRY_DSN
    - poetry install
    - source .venv/bin/activate
    - alembic --config alembic.ini upgrade head
    - pytest --junitxml ${CI_PROJECT_DIR}/pytest.xml
  rules: *auto-main-auto-mr
  artifacts:
    when: always
    reports:
      junit: ${CI_PROJECT_DIR}/pytest.xml
  cache:
    key: global
    paths:
      - .poetry-cache
  tags:
    - dpt-azure

integration-tests-image:
  stage: test-image
  dependencies: []
  image: crindiebimain.azurecr.io/dpt/dataset-manager:commit-$CI_COMMIT_SHORT_SHA
  services:
    - name: crindiebimain.azurecr.io/cpt/local-sql-server-2022:master
      alias: mssql_db
    - name: mcr.microsoft.com/azure-storage/azurite:3.32.0
      alias: azurite
  variables:
    SQLALCHEMY_DATABASE_URL: "mssql+pyodbc://indiebi:Password1!@mssql_db/Shards?driver=ODBC+Driver+17+for+SQL+Server"
    AZURITE_HOST: azurite
    GIT_STRATEGY: none
  script:
    - cd /app
    - alembic --config alembic.ini upgrade head
    - python -m pytest --junitxml ${CI_PROJECT_DIR}/pytest.xml  -p no:cacheprovider
  rules: *auto-main-auto-mr
  artifacts:
    when: always
    reports:
      junit: ${CI_PROJECT_DIR}/pytest.xml
  tags:
    - dpt-azure

lints:
  stage: prepare
  image: registry.gitlab.com/bluebrick/indiebi/infra/python-ci-image:python-3.10
  script:
    - unset SENTRY_DSN
    - poetry install
    - source .venv/bin/activate
    - ruff format . --check
    - ruff check .
    - mypy .
  rules: *auto-main-auto-mr
  cache:
    key: global
    paths:
      - .poetry-cache
  tags:
    - dpt-azure

build-image:
  stage: prepare
  dependencies: []
  image:
    name: crindiebimain.azurecr.io/dpt/image-builder:prod
  script:
    - image-exists && echo "Image already exists, skipping build!" && exit 0
    - image-builder
  tags:
    - dpt-azure
  rules: *auto-main-auto-mr

.deploy:
  dependencies: []
  variables:
    GIT_STRATEGY: none
  stage: deploy
  image:
    name: crindiebimain.azurecr.io/dpt/image-builder:prod
  script:
    - tag-image $ENV
  allow_failure: false
  tags:
    - dpt-azure

deploy-dev:
  extends: .deploy
  variables:
    ENV: dev
  rules: *auto-main-manual-mr

deploy-prod:
  extends: .deploy
  variables:
    ENV: prod
  rules: *manual-main
