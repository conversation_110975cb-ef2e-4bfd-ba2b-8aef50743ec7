from enum import Enum

import fire
import requests

from dataset_manager.azure.powerbi.service import get_client_token


class PBIRefreshStatus(Enum):
    """https://docs.microsoft.com/en-us/rest/api/power-bi/datasets/get-refresh-history"""

    UNKNOWN = "Unknown"
    COMPLETED = "Completed"
    FAILED = "Failed"
    DISABLED = "Disabled"


def get_last_refresh_status(group_id: str, dataset_id: str) -> str | None:
    url = f"https://api.powerbi.com/v1.0/myorg/groups/{group_id}/datasets/{dataset_id}/refreshes?$top=1"
    headers = {"Authorization": get_client_token()}
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    last_refresh: list[dict] = response.json()["value"]
    if len(last_refresh) == 0:
        return None
    return last_refresh[0]["status"]


def start_refresh(workspace_id: str, dataset_id: str):
    url = f"https://api.powerbi.com/v1.0/myorg/groups/{workspace_id}/datasets/{dataset_id}/refreshes"
    headers = {"Authorization": get_client_token()}
    data = {"refresh": {"objects": [{"database": "Dataset"}], "type": "full"}}
    response = requests.post(
        url,
        json=data,
        headers=headers,
    )
    if response.status_code == 200 or response.status_code == 202:
        return response
    else:
        raise Exception("Cannot set refresh")


def start_refreshing_shard(shard):
    print(f"""refreshing {shard["studio_id"]}""")
    shard_status = get_last_refresh_status(shard["workspace_id"], shard["dataset_id"])
    if shard_status != PBIRefreshStatus.UNKNOWN.value:
        start_refresh(shard["workspace_id"], shard["dataset_id"])


def get_shard_list():
    url = "https://dm.indiebi.com/shard"
    headers = {"x-api-key": "..."}

    response = requests.get(url, headers=headers)

    try:
        response.raise_for_status()
        return response.json()
    except Exception as ex:
        print(response.json())
        raise ex


def refresh_shards():
    shard_list = get_shard_list()
    for shard in shard_list:
        start_refreshing_shard(shard)


if __name__ == "__main__":
    fire.Fire(refresh_shards)
