from pprint import pprint
from typing import Callable

import fire

from dataset_manager.azure.powerbi.service import request


class PowerBIAPI:
    def __init__(self, request_handler: Callable) -> None:
        self._request_handler = request_handler

    def get(self, url, **kwargs):
        return self._request_handler(method="get", url=url, **kwargs)

    def post(self, url, **kwargs):
        return self._request_handler(method="post", url=url, **kwargs)

    def patch(self, url, **kwargs):
        return self._request_handler(method="patch", url=url, **kwargs)

    def delete(self, url, **kwargs):
        return self._request_handler(method="delete", url=url, **kwargs)


class CommandLine:
    def __init__(self) -> None:
        self._powerbi_api = PowerBIAPI(request_handler=request)

    def by_name(self, name: str, confirm_on_every_row: bool = False):
        resp = self._powerbi_api.get(
            "https://api.powerbi.com/v1.0/myorg/groups",
            params={"$filter": f"contains(name, '{name}')"},
        )
        workspaces_found = resp.json()["value"]

        print(f"{len(workspaces_found)} studios found for deletion:")
        pprint([w["name"] for w in workspaces_found])

        if not confirm_on_every_row:
            while (confirm := input("Continue? (y/n) ")) not in ("y", "n"):
                continue

            if confirm != "y":
                print("Exiting without removing workspaces!")
                return

        for workspace in workspaces_found:
            if confirm_on_every_row:
                while (
                    confirm := input(f"Remove workspace {workspace['name']}? (y/n) ")
                ) not in ("y", "n"):
                    continue

                if confirm != "y":
                    print("Skipping!")
                    continue

            print(f"Removing: {workspace}")
            self._powerbi_api.delete(
                f"https://api.powerbi.com/v1.0/myorg/groups/{workspace['id']}"
            )


def main():
    fire.Fire(CommandLine)


if __name__ == "__main__":
    main()
