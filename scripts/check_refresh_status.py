from enum import Enum

import fire
import requests

from dataset_manager.azure.powerbi.service import get_client_token

completed = []
failed = []
unknown = []
disabled = []


class PBIRefreshStatus(Enum):
    """https://docs.microsoft.com/en-us/rest/api/power-bi/datasets/get-refresh-history"""

    UNKNOWN = "Unknown"
    COMPLETED = "Completed"
    FAILED = "Failed"
    DISABLED = "Disabled"


def get_last_refresh_status(group_id: str, dataset_id: str) -> str | None:
    response = requests.get(
        f"https://api.powerbi.com/v1.0/myorg/groups/{group_id}/datasets/{dataset_id}/refreshes?$top=1",
        headers={"Authorization": get_client_token()},
    )
    response.raise_for_status()
    last_refresh: list[dict] = response.json()["value"]
    if len(last_refresh) == 0:
        return None
    return last_refresh[0]["status"]


def check_shard_refresh_status(shard):
    shard_status = get_last_refresh_status(shard["workspace_id"], shard["dataset_id"])

    if shard_status == PBIRefreshStatus.COMPLETED.value:
        completed.append(shard["studio_id"])
    elif shard_status == PBIRefreshStatus.FAILED.value:
        failed.append(shard["studio_id"])
    elif shard_status == PBIRefreshStatus.UNKNOWN.value:
        unknown.append(shard["studio_id"])
    else:
        disabled.append(shard["studio_id"])


def get_shard_list():
    url = "https://dm.indiebi.com/shard"
    headers = {"x-api-key": "..."}

    response = requests.get(url, headers=headers)

    try:
        response.raise_for_status()
        return response.json()
    except Exception as ex:
        print(response.json())
        raise ex


def check_refresh_status():
    shard_list = get_shard_list()
    for shard in shard_list:
        check_shard_refresh_status(shard)
    return print(
        f"completed: {len(completed)} failed: {len(failed)} unknown: {len(unknown)} disabled: {len(disabled)} total: {len(completed) + len(unknown) + len(failed) + len(disabled)}"
    )


if __name__ == "__main__":
    fire.Fire(check_refresh_status)
