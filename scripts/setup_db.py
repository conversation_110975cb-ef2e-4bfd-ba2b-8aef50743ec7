import argparse
import struct
from string import Template
from typing import Dict, Optional

import pyodbc
from azure.identity import DefaultAzureCredential

from dataset_manager.config import DB_TOKEN_URL, SQL_COPT_SS_ACCESS_TOKEN


def get_connection_string(server: str, database: Optional[str] = None):
    conn_string = f"DRIVER=ODBC Driver 17 for SQL Server;SERVER={server}"
    return f"{conn_string};DATABASE={database}" if database else conn_string


def get_azure_token():
    azure_credentials = DefaultAzureCredential()
    raw_token = azure_credentials.get_token(DB_TOKEN_URL).token.encode("utf-16-le")
    return struct.pack(f"<I{len(raw_token)}s", len(raw_token), raw_token)


def connect(server: str, database: Optional[str] = None):
    return pyodbc.connect(
        get_connection_string(server, database),
        attrs_before={SQL_COPT_SS_ACCESS_TOKEN: get_azure_token()},
    )


def run_query(connection, query: str):
    with connection.cursor() as cursor:
        cursor.execute(query)


def parse_query_template(query: str, substitutions: Dict[str, str]):
    template = Template(query)
    return template.safe_substitute(substitutions)


def setup_db(server: str, k8s_identity: str, gitlab_sp_name: str):
    """Create users and schema in the SQL database."""
    connection = connect(server, database="Shard")

    with open("sql_scripts/create_users.sql.template", "r") as f:
        template = f.read()
        run_query(
            connection,
            parse_query_template(
                template,
                {"k8s_identity": k8s_identity, "gitlab_sp": gitlab_sp_name},
            ),
        )

    with open("sql_scripts/create_schema.sql", "r") as f:
        query = f.read()
        run_query(connection, query)

    connection.close()


def main():
    parser = argparse.ArgumentParser(description="Create SQL database users.")
    parser.add_argument(
        "db_host", metavar="database-host", type=str, help="SQL host url"
    )
    parser.add_argument(
        "k8s_identity",
        metavar="identity-name",
        type=str,
        help="Azure Identity name assigned to k8s pods running the service",
    )
    parser.add_argument(
        "gitlab_sp_name",
        metavar="sp-name",
        type=str,
        help="GitLab CI Service Principal name",
    )
    args = parser.parse_args()
    setup_db(args.db_host, args.k8s_identity, args.gitlab_sp_name)


if __name__ == "__main__":
    main()
