import fire
import requests

from dataset_manager.azure.powerbi.service import get_client_token


def delete_profile(profile_id: str):
    url = f"https://api.powerbi.com/v1.0/myorg/profiles/{profile_id}"
    headers = {
        "Authorization": get_client_token(),
    }

    response = requests.delete(url, headers=headers)

    try:
        response.raise_for_status()
    except Exception as ex:
        print(response.json())
        raise ex


if __name__ == "__main__":
    fire.Fire(delete_profile)
