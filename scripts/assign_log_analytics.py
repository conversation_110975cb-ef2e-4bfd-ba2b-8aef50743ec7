from functools import partial

import fire
import requests

from dataset_manager.azure.client import AzureAPIClient, auth_token_provider

DM_API_URL = "https://dataset-manager.indiebi.com/"
DM_API_KEY = "ibit-XXX"

DEV_LA_WORKSPACE = {
    "subscriptionId": "03db22d5-c3b5-4c7c-87c1-81da04c6283f",
    "resourceGroup": "rg-pbi-embedded-dev",
    "resourceName": "la-pbi-embedded-dev",
}

LA_WORKSPACE = {
    "subscriptionId": "03db22d5-c3b5-4c7c-87c1-81da04c6283f",
    "resourceGroup": "rg-pbi-embedded-prod",
    "resourceName": "la-pbi-embedded-prod",
}

ADMINS = [
    {
        "groupUserAccessRight": "Admin",
        "displayName": "Aleksander Magda",
        "identifier": "<EMAIL>",
        "principalType": "User",
        "emailAddress": "<EMAIL>",
    }
]


class PBIAdminAPI:
    def __init__(self, azure_api_client: AzureAPIClient) -> None:
        self._api: AzureAPIClient = azure_api_client
        self._base_url: str = "https://api.powerbi.com/v1.0/myorg/admin/groups"

    def assign_log_analytics_workspace(
        self, workspace_id: str, log_analytics_workspace: dict
    ):
        self._api.patch(
            url=f"{self._base_url}/{workspace_id}",
            json={
                "logAnalyticsWorkspace": log_analytics_workspace,
            },
        )

    def assign_admins(self, workspace_id: str, admins) -> None:
        existing_users = self._get_users(workspace_id=workspace_id)
        if existing_users:
            existing_users = [user["identifier"] for user in existing_users]

        for user in admins:
            if user["identifier"] in existing_users:
                continue
            self._assign_user_to_workspace(workspace_id, user)

    def _get_users(self, workspace_id: str) -> list:
        response = self._api.get(f"{self._base_url}/{workspace_id}/users")
        return response.json()["value"]

    def _assign_user_to_workspace(self, workspace_id: str, user):
        self._api.post(f"{self._base_url}/{workspace_id}/users", json=user)


def get_active_shard_list():
    url = f"{DM_API_URL}/shard/active"
    headers = {"x-api-key": DM_API_KEY}

    response = requests.get(url, headers=headers)

    try:
        response.raise_for_status()
        return response.json()
    except Exception as ex:
        print(response.json())
        raise ex


def main():
    azure_api_client = AzureAPIClient(
        auth_token_provider=partial(
            auth_token_provider, "https://analysis.windows.net/powerbi/api/.default"
        )
    )
    admin_api = PBIAdminAPI(azure_api_client)
    shards = get_active_shard_list()

    for shard in shards:
        print(f"assigning {shard['workspace_id']}, {shard['workspace_name']}")
        admin_api.assign_admins(shard["workspace_id"], ADMINS)
        admin_api.assign_log_analytics_workspace(shard["workspace_id"], LA_WORKSPACE)


if __name__ == "__main__":
    fire.Fire(main)
