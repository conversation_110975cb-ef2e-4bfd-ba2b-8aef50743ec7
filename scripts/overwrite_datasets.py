import os
from pathlib import Path
from time import sleep

import fire
import requests
from requests_toolbelt import MultipartEncoder

from dataset_manager.azure.powerbi.service import get_client_token

os.environ["AZURE_CLIENT_ID"] = "appClientId"
os.environ["AZURE_TENANT_ID"] = "appTenant"
os.environ["AZURE_CLIENT_SECRET"] = "appSecret"


def start_refresh(workspace_id: str, dataset_id: str):
    url = f"https://api.powerbi.com/v1.0/myorg/groups/{workspace_id}/datasets/{dataset_id}/refreshes"
    headers = {"Authorization": get_client_token()}
    json = ({"refresh": {"objects": [{"database": "Dataset"}], "type": "full"}},)
    response = requests.post(
        url,
        json=json,
        headers=headers,
    )

    if response.status_code == 200 or response.status_code == 202:
        return response
    else:
        raise Exception("Cannot set refresh")


def deploy_dataset(workspace_id: str) -> str:
    imports_id = _import_pbix(workspace_id)
    return _get_dataset_id(imports_id, workspace_id)


def _import_pbix(workspace_id: str):
    pbix_pth = Path(".")
    for visual_file in pbix_pth.glob("*.pbix"):
        file_pth = Path(visual_file)
        display_name = "V1"

        url = f"https://api.powerbi.com/v1.0/myorg/groups/{workspace_id}/"
        url += f"imports?datasetDisplayName={display_name}"
        url += "&nameConflict=CreateOrOverwrite"
        encoder = MultipartEncoder(
            {"value": (None, open(file_pth, "rb"), "multipart/form-data")}
        )
        headers = {
            "Authorization": get_client_token(),
            "Content-Type": "multipart/form-data",
        }
        response = requests.post(url, data=encoder, headers=headers)
        if response.status_code == 200 or response.status_code == 202:
            return response.json()["id"]
        else:
            raise Exception("Cannot upload pbix")


def _get_dataset_id(import_id: str, workspace_id: str) -> str:
    url = (
        f"https://api.powerbi.com/v1.0/myorg/groups/{workspace_id}/imports/{import_id}"
    )
    headers = {
        "Authorization": get_client_token(),
        "Content-Type": "application/json",
    }
    resp = requests.get(url, headers=headers)
    if resp.status_code == 200:
        while resp.json()["importState"] != "Succeeded":
            sleep(5)
            resp = requests.get(url, headers=headers)
        return resp.json()["datasets"][0]["id"]
    else:
        raise Exception("Cannot get dataset")


# Set dataset credentials
def _get_datasources(workspace_id: str, dataset_id: str) -> str:
    url = f"https://api.powerbi.com/v1.0/myorg/groups/{workspace_id}/datasets/{dataset_id}/datasources"
    headers = {
        "Authorization": get_client_token(),
        "Content-Type": "application/json",
    }
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return response.json()["value"]
    else:
        raise Exception("Cannot get datasources")


def set_credentials_for_datasource(workspace_id: str, dataset_id: str):
    datasources = _get_datasources(workspace_id, dataset_id)
    ds_object = datasources[0]
    url = f"https://api.powerbi.com/v1.0/myorg/gateways/{ds_object['gatewayId']}/datasources/{ds_object['datasourceId']}"
    headers = {
        "Authorization": get_client_token(),
        "Content-Type": "application/json",
    }
    key = ""
    data = {
        "credentialDetails": {
            "credentialType": "Key",
            "credentials": '{"credentialData":[{"name":"key", "value":"'
            + f"{key}"
            + '"}]}',
            "encryptedConnection": "Encrypted",
            "encryptionAlgorithm": "None",
            "privacyLevel": "Organizational",
        }
    }

    response = requests.patch(url, json=data, headers=headers)
    if response.status_code != 200:
        raise Exception("Cannot set credentials")


def set_studio_id(workspace_id: str, dataset_id: str, studio_id: int):
    url = f"https://api.powerbi.com/v1.0/myorg/groups/{workspace_id}/datasets/{dataset_id}/Default.UpdateParameters"
    headers = {
        "Authorization": get_client_token(),
        "Content-Type": "application/json",
    }
    data = {
        "updateDetails": [
            {"name": "studio_id_parameter", "newValue": str(studio_id)},
        ]
    }

    response = requests.post(url, json=data, headers=headers)
    if response.status_code != 200:
        raise Exception("Cannot set studio_id")


def reupload_dataset(workspace_id: str, studio_id: int):
    print(f"uploading new dataset for studio {studio_id}")
    dataset_id = deploy_dataset(workspace_id)
    set_studio_id(workspace_id, dataset_id, studio_id)
    set_credentials_for_datasource(workspace_id, dataset_id)
    start_refresh(workspace_id, dataset_id)
    print(studio_id, dataset_id, "new dataset_id")


def overwrite_datasets(shard):
    reupload_dataset(shard["workspace_id"], shard["studio_id"])


if __name__ == "__main__":
    fire.Fire(overwrite_datasets)
